# Family Registration System Documentation

Welcome to the comprehensive documentation for the Family Registration System. This documentation provides detailed information about the system's design, implementation, and progress for the Barangay La Huerta Health Information Management Unit (HIMU).

## Table of Contents

### Core Documentation
- [Project Brief](project_brief.md) - Overview of the system, its purpose, and core features
- [Product Context](product_context.md) - Organizational context, user profiles, and business value
- [System Patterns](system_patterns.md) - Architecture, design patterns, and code organization

### Development Documentation
- [Active Context](active_context.md) - Current development focus and active components
- [Progress](progress.md) - Project timeline, achievements, and upcoming work

## Quick Links

- **For New Team Members**: Start with the [Project Brief](project_brief.md) and [Product Context](product_context.md)
- **For Developers**: Review the [System Patterns](system_patterns.md) and [Active Context](active_context.md)
- **For Project Managers**: Check the [Progress](progress.md) document for timeline and status

## Documentation Updates

This documentation is maintained alongside the codebase and updated regularly to reflect the current state of the project. Last updated: May 2025. 