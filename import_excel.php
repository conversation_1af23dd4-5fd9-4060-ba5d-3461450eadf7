<?php
session_start();
require 'db.php';

// Increase PHP limits for large file processing
set_time_limit(300); // 5 minutes
ini_set('memory_limit', '256M');

// Check if user is logged in and is admin
if (!isset($_SESSION['logged_in']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'super admin')) {
    header('Location: dashboard.php');
    exit();
}

// Check if a file was uploaded
if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
    $_SESSION['error'] = 'Error uploading file. Please try again.';
    header('Location: dashboard.php');
    exit();
}

// Check if the import tracking tables exist
// Check if import_batches table exists, create it if not
$check_batch_table = $conn->query("SHOW TABLES LIKE 'import_batches'");
if ($check_batch_table->num_rows == 0) {
    // Table doesn't exist, create it
    $create_table_sql = "CREATE TABLE import_batches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        batch_identifier VARCHAR(255) NOT NULL,
        import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        imported_by VARCHAR(255) NOT NULL,
        record_count INT NOT NULL,
        has_been_undone BOOLEAN DEFAULT FALSE
    )";
    
    if (!$conn->query($create_table_sql)) {
        $_SESSION['error'] = "Failed to create import tracking table: " . $conn->error;
        header("Location: dashboard.php");
        exit();
    }
}

// Check if the family_registration table has a batch_identifier column, add it if not
$check_batch_column = $conn->query("SHOW COLUMNS FROM family_registration LIKE 'batch_identifier'");
if ($check_batch_column->num_rows == 0) {
    // Column doesn't exist, add it
    $add_column_sql = "ALTER TABLE family_registration ADD COLUMN batch_identifier VARCHAR(255) NULL";
    
    if (!$conn->query($add_column_sql)) {
        $_SESSION['error'] = "Failed to add batch tracking to family records: " . $conn->error;
        header("Location: dashboard.php");
        exit();
    }
}

// Generate a unique batch identifier
$batch_identifier = 'IMPORT_' . date('YmdHis') . '_' . uniqid();

try {
    // Begin transaction to ensure data consistency
    $conn->begin_transaction();
    
    // Open the uploaded file
    $handle = fopen($_FILES['excel_file']['tmp_name'], 'r');
    
    if ($handle === false) {
        throw new Exception('Error opening file');
    }

    // Skip header row
    fgetcsv($handle);

    // Prepare the insert statement (now with batch_identifier)
    $stmt = $conn->prepare("INSERT INTO family_registration (registration_date, family_number, male_name, female_name, address, registered_by, remarks, batch_identifier) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

    // Counter for successful imports
    $imported = 0;
    $errors = [];

    // Process each row
    while (($row = fgetcsv($handle)) !== false) {
        // Regular check to keep PHP responsive
        if ($imported % 100 === 0) {
            // Free up memory periodically
            gc_collect_cycles();
        }

        if (empty($row[0])) continue; // Skip empty rows

        // Convert date format if needed
        $registration_date = date('Y-m-d', strtotime($row[0]));
        
        // Convert all text fields to uppercase
        $family_number = strtoupper(trim($row[1]));
        $male_name = strtoupper(trim($row[2]));
        $female_name = strtoupper(trim($row[3]));
        $address = strtoupper(trim($row[4]));
        $remarks = isset($row[5]) ? strtoupper(trim($row[5])) : ''; // Get remarks from the 6th column
        $registered_by = isset($row[6]) ? strtoupper(trim($row[6])) : ''; // Make registered_by optional
        
        // Check if registered_by exists in users table
        if (!empty($registered_by)) {
            $check_user = $conn->prepare("SELECT username FROM users WHERE username = ?");
            $check_user->bind_param("s", $registered_by);
            $check_user->execute();
            $check_result = $check_user->get_result();
            
            // If user doesn't exist, set to current user or NULL
            if ($check_result->num_rows == 0) {
                $registered_by = $_SESSION['username']; // Default to current user
            }
            
            $check_user->close();
        } else {
            // If empty, set to current user who is doing the import
            $registered_by = $_SESSION['username'];
        }

        try {
            $stmt->bind_param("ssssssss", 
                $registration_date,
                $family_number,
                $male_name,
                $female_name,
                $address,
                $registered_by,
                $remarks,
                $batch_identifier // Add batch identifier to each record
            );

            if ($stmt->execute()) {
                $imported++;
            } else {
                $errors[] = "Error importing row with Family Number: $family_number - " . $stmt->error;
            }
        } catch (Exception $e) {
            $errors[] = "Error importing row with Family Number: $family_number - " . $e->getMessage();
        }
    }

    fclose($handle);
    $stmt->close();
    
    // Record this import batch if any records were imported
    if ($imported > 0) {
        $batch_stmt = $conn->prepare("INSERT INTO import_batches (batch_identifier, imported_by, record_count) VALUES (?, ?, ?)");
        $imported_by = $_SESSION['username'];
        
        $batch_stmt->bind_param("ssi", $batch_identifier, $imported_by, $imported);
        $batch_stmt->execute();
        $batch_stmt->close();
        
        // Optional: Log the import operation
        $log_sql = "INSERT INTO system_log (action, performed_by, details) VALUES (?, ?, ?)";
        
        // Check if system_log table exists
        $check_log_table = $conn->query("SHOW TABLES LIKE 'system_log'");
        if ($check_log_table->num_rows > 0) {
            $log_stmt = $conn->prepare($log_sql);
            $action = "IMPORT_RECORDS";
            $details = json_encode([
                'batch_identifier' => $batch_identifier,
                'record_count' => $imported,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
            $log_stmt->bind_param("sss", $action, $imported_by, $details);
            $log_stmt->execute();
            $log_stmt->close();
        }
    }
    
    // Commit the transaction
    $conn->commit();

    // Show results
    $_SESSION['success'] = "Successfully imported $imported records.";
    if (!empty($errors)) {
        // Limit the number of errors shown
        $errorCount = count($errors);
        if ($errorCount > 10) {
            $errors = array_slice($errors, 0, 10);
            $errors[] = "... and " . ($errorCount - 10) . " more errors";
        }
        $_SESSION['warning'] = "Errors occurred:\n" . implode("\n", $errors);
    }

    header('Location: dashboard.php');
    exit();

} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    
    $_SESSION['error'] = 'Error processing file: ' . $e->getMessage();
    header('Location: dashboard.php');
    exit();
} finally {
    $conn->close();
}
?> 