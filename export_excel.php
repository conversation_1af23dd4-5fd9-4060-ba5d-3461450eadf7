<?php
session_start();
if (!isset($_SESSION['username']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'super admin')) {
    header("Location: login.php");
    exit();
}

include "db.php";

// Set headers for Excel download
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="family_records_' . date('Y-m-d') . '.xls"');
header('Pragma: no-cache');
header('Expires: 0');

// Get all family records
$sql = "SELECT * FROM family_registration ORDER BY registration_date DESC";
$result = $conn->query($sql);

// Output Excel headers
echo "Registration Date\tFamily Number\tMale Name\tFemale Name\tAddress\tRemarks\tRegistered By\n";

// Output data rows
while ($row = $result->fetch_assoc()) {
    echo date('Y-m-d', strtotime($row['registration_date'])) . "\t";
    echo $row['family_number'] . "\t";
    echo $row['male_name'] . "\t";
    echo $row['female_name'] . "\t";
    echo $row['address'] . "\t";
    echo $row['remarks'] . "\t";
    echo $row['registered_by'] . "\n";
}

$conn->close();
?> 