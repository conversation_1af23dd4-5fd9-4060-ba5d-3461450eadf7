<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'super admin') {
    header("Location: dashboard.php");
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if the deleted_records table exists
$table_check = $conn->query("SHOW TABLES LIKE 'deleted_records'");
$table_exists = $table_check->num_rows > 0;

// Handle search and filtering parameters
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build the SQL query with search filters
$where_clauses = [];
$params = [];
$param_types = '';

if (!empty($search_term)) {
    $where_clauses[] = "(family_number LIKE ? OR male_name LIKE ? OR female_name LI<PERSON> ?)";
    $search_param = '%' . $search_term . '%';
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

// Handle restore action
if (isset($_POST['restore']) && is_numeric($_POST['restore'])) {
    $id = (int)$_POST['restore'];
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Get the record to restore
        $select_stmt = $conn->prepare("SELECT * FROM deleted_records WHERE id = ?");
        $select_stmt->bind_param("i", $id);
        $select_stmt->execute();
        $result = $select_stmt->get_result();
        
        if ($record = $result->fetch_assoc()) {
            // Insert back into family_registration table
            $insert_stmt = $conn->prepare("INSERT INTO family_registration 
                (registration_date, family_number, male_name, female_name, address, remarks, registered_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)");
                
            $insert_stmt->bind_param(
                "sssssss", 
                $record['registration_date'],
                $record['family_number'],
                $record['male_name'],
                $record['female_name'],
                $record['address'],
                $record['remarks'],
                $record['registered_by']
            );
            
            if (!$insert_stmt->execute()) {
                throw new Exception("Failed to restore record: " . $insert_stmt->error);
            }
            
            // Delete from deleted_records table
            $delete_stmt = $conn->prepare("DELETE FROM deleted_records WHERE id = ?");
            $delete_stmt->bind_param("i", $id);
            
            if (!$delete_stmt->execute()) {
                throw new Exception("Failed to remove from deleted records: " . $delete_stmt->error);
            }
            
            // Commit transaction
            $conn->commit();
            $success_message = "Record successfully restored.";
        } else {
            throw new Exception("Record not found in deleted records.");
        }
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $error_message = $e->getMessage();
    }
}

// Handle permanent delete
if (isset($_POST['permanent_delete']) && is_numeric($_POST['permanent_delete'])) {
    $id = (int)$_POST['permanent_delete'];
    
    $delete_stmt = $conn->prepare("DELETE FROM deleted_records WHERE id = ?");
    $delete_stmt->bind_param("i", $id);
    
    if ($delete_stmt->execute()) {
        $success_message = "Record permanently deleted.";
    } else {
        $error_message = "Failed to permanently delete record: " . $delete_stmt->error;
    }
}

// Handle delete all records
if (isset($_POST['delete_all']) && $_POST['delete_all'] == 1) {
    $delete_all_stmt = $conn->prepare("DELETE FROM deleted_records");
    
    if ($delete_all_stmt->execute()) {
        $success_message = "All records have been permanently deleted.";
    } else {
        $error_message = "Failed to delete all records: " . $delete_all_stmt->error;
    }
}

// Pagination
$records_per_page = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Get total number of records
$total_records = 0;
if ($table_exists) {
    if (!empty($where_clauses)) {
        $count_sql = "SELECT COUNT(*) as count FROM deleted_records WHERE " . implode(' AND ', $where_clauses);
        $count_stmt = $conn->prepare($count_sql);
        
        if (!empty($params)) {
            $count_stmt->bind_param($param_types, ...$params);
        }
        
        $count_stmt->execute();
        $count_result = $count_stmt->get_result();
        $total_records = $count_result->fetch_assoc()['count'];
    } else {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM deleted_records");
        $total_records = $count_result->fetch_assoc()['count'];
    }
}
$total_pages = ceil($total_records / $records_per_page);

// Get records with pagination
$deleted_records = [];
if ($table_exists) {
    $sql = "SELECT * FROM deleted_records";
    
    if (!empty($where_clauses)) {
        $sql .= " WHERE " . implode(' AND ', $where_clauses);
    }
    
    $sql .= " ORDER BY deleted_at DESC LIMIT ?, ?";
    
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $params[] = $offset;
        $params[] = $records_per_page;
        $param_types .= 'ii';
        $stmt->bind_param($param_types, ...$params);
    } else {
        $stmt->bind_param('ii', $offset, $records_per_page);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $deleted_records[] = $row;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recently Deleted Records - Family Number Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            min-height: 100vh;
            background: #f0f7ff;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }
        
        .dropdown-container.show {
            display: block;
        }
        
        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
        }

        .container {
            max-width: 98%;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            animation: scaleIn 0.5s ease-out forwards;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            animation: fadeIn 0.6s ease-out forwards;
        }

        .header h2 {
            color: #000;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-bottom: 2rem;
            scrollbar-width: none;
            -ms-overflow-style: none;
            max-width: 100%;
        }

        .table-container::-webkit-scrollbar {
            display: none;
        }

        table {
            width: 100%;
            min-width: 1200px;
            border-collapse: collapse;
            margin-bottom: 1rem;
            font-size: 14px;
            table-layout: auto;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
            color: #000;
            white-space: normal;
            overflow: hidden;
            word-wrap: break-word;
        }

        th {
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            font-weight: 700;
            position: sticky;
            top: 0;
            z-index: 10;
            overflow: hidden;
        }
        
        th::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
            z-index: 2;
        }

        tr:nth-child(even) {
            background: #f8f9fa;
        }

        tr:hover {
            background: #f1f3f4;
        }

        .family-number {
            font-weight: 600;
        }

        .action-buttons {
            display: flex;
            gap: 0.2rem;
            align-items: center;
            justify-content: center;
        }

        .action-btn {
            padding: 0.35rem;
            width: 24px;
            height: 24px;
            font-size: 0.75rem;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .delete-btn {
            background: #f8f9fa;
            border-color: #e74c3c;
            color: #e74c3c;
        }

        .delete-btn:hover {
            background: #e74c3c;
            color: #000;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(231, 76, 60, 0.2);
        }

        .delete-forever-btn {
            background: #f8f9fa;
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }
        
        .delete-forever-btn:hover {
            background: #e74c3c;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(231, 76, 60, 0.2);
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .pagination a {
            padding: 0.5rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            text-decoration: none;
            color: #000;
            transition: all 0.3s ease;
        }

        .pagination a:hover {
            background: #f8f9fa;
            border-color: #4db3a8;
        }

        .pagination .active {
            background: #4db3a8;
            color: #000;
            border-color: #4db3a8;
            font-weight: 600;
        }

        .pagination .disabled {
            color: #000;
            opacity: 0.5;
            pointer-events: none;
        }

        .no-records {
            text-align: center;
            padding: 2rem;
            color: #000;
            font-size: 1.1rem;
            opacity: 0;
            animation: fadeIn 0.8s ease-out 0.5s forwards;
        }

        .no-records i {
            margin-right: 10px;
            color: #4db3a8;
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .success {
            background: #d4edda;
            border-left: 4px solid #28a745;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }

        .deleted-date {
            font-size: 0.8rem;
            color: #777;
            display: block;
        }

        .deleted-by {
            font-weight: 500;
        }

        .deleted-record {
            animation: fadeIn 0.5s ease-out forwards;
            position: relative;
        }

        .deleted-record::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: #e74c3c;
            opacity: 0.5;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.95);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Confirmation modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            position: relative;
            background: white;
            margin: 15% auto;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            animation: scaleIn 0.3s ease-out;
        }

        .modal-header {
            margin-bottom: 1.5rem;
        }

        .modal-header h3 {
            color: #000;
            margin-bottom: 0.5rem;
        }

        .modal-body {
            margin-bottom: 1.5rem;
        }

        .modal-body p {
            color: #000;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        .modal-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background: #ecf0f1;
            color: #000;
            margin-right: 10px;
        }

        .confirm-btn {
            background: #27ae60;
            color: white;
            font-weight: 600;
        }

        .confirm-restore-btn {
            background: #27ae60;
            color: white;
        }

        .confirm-delete-btn {
            background: #e74c3c;
            color: white;
        }

        .close-modal {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .container {
                padding: 1rem;
            }

            .header h2 {
                font-size: 1.8rem;
            }

            .table-container {
                overflow-x: auto;
            }

            table {
                min-width: 900px;
            }
        }

        .table-container::after {
            content: "";
            display: block;
            height: 10px;
            width: 100%;
        }

        /* Special handling for deletion info column */
        th:nth-child(8), td:nth-child(8) {
            white-space: normal;
        }
        
        /* Class-based selectors for better control */
        .unwrapped-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }
        
        .deletion-info {
            white-space: normal;
            word-wrap: break-word;
        }

        /* Class for buttons in modal */
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .delete-btn.btn {
            background: #e74c3c;
            color: white;
            font-weight: 600;
        }

        .delete-btn.btn:hover {
            background: #c0392b;
        }

        .search-wrapper {
            display: flex;
            gap: 1rem;
            align-items: center;
            justify-content: center;
        }

        .search-input {
            flex: 1;
            padding: 0.8rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: #000;
        }

        .search-input:focus {
            outline: none;
            border-color: #4db3a8;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        .filter-select {
            padding: 0.8rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: #000;
            min-width: 150px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #4db3a8;
        }

        .info-box {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .info-box i {
            margin-right: 10px;
            color: #4db3a8;
        }

        .search-btn {
            padding: 0.8rem 1.5rem;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .clear-search {
            padding: 0.8rem 1.5rem;
            background: #e74c3c;
            color: #000;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .clear-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        
        .search-btn::after, .clear-search::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        .delete-all-btn {
            margin-left: auto;
            padding: 0.5rem 1rem;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .delete-all-btn:hover {
            background: #c0392b;
            box-shadow: 0 2px 5px rgba(231, 76, 60, 0.3);
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link dropdown-btn active">
                        <i class="fas fa-folder"></i>
                        Records
                    </a>
                    <div class="dropdown-container show">
                        <a href="view_family_records.php" class="dropdown-link">
                            <i class="fas fa-table"></i>
                            View Records
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                            <i class="fas fa-exclamation-triangle"></i>
                            Duplicate Records
                        </a>
                        <a href="missing_family_numbers.php" class="dropdown-link">
                            <i class="fas fa-list-ol"></i>
                            Missing Family Numbers
                        </a>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <a href="recently_deleted.php" class="dropdown-link active">
                            <i class="fas fa-trash-alt"></i>
                            Recently Deleted
                        </a>
                        <?php endif; ?>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                </li>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-content">
            <div class="container">
                <div class="header">
                    <h2>Recently Deleted Records</h2>
                    
                </div>
                
                <?php if (isset($success_message)): ?>
                    <div class="message success" style="background: #e7f7ef; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #4db3a8; display: flex; align-items: center;">
                        <i class="fas fa-check-circle" style="margin-right: 10px; font-size: 1.2rem; color: #4db3a8;"></i>
                        <div><?php echo $success_message; ?></div>
                        <span class="close-message" onclick="this.parentNode.style.display='none'" style="margin-left: auto; cursor: pointer;">&times;</span>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="message error" style="background: #fff5f5; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #e53e3e; display: flex; align-items: center;">
                        <i class="fas fa-times-circle" style="margin-right: 10px; font-size: 1.2rem; color: #e53e3e;"></i>
                        <div><?php echo $error_message; ?></div>
                        <span class="close-message" onclick="this.parentNode.style.display='none'" style="margin-left: auto; cursor: pointer;">&times;</span>
                    </div>
                <?php endif; ?>

                <!-- Add search and filter options -->
                <div class="search-container">
                    <form method="GET" action="" class="search-form">
                        <div class="search-wrapper">
                            <input type="text" name="search" placeholder="Search by name or family number..." 
                                   value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>"
                                   class="search-input">
                            
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i> Search
                            </button>
                            
                            <?php if (isset($_GET['search'])): ?>
                                <a href="recently_deleted.php" class="clear-search">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>

                <!-- Add info box for total records -->
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    <span>Total deleted records: <strong><?php echo $total_records; ?></strong></span>
                    
                    <?php if ($table_exists && !empty($deleted_records)): ?>
                    <button type="button" id="deleteAllBtn" class="delete-all-btn">
                        <i class="fas fa-trash-alt"></i> Delete All Records
                    </button>
                    <?php endif; ?>
                </div>

                <div class="table-container">
                    <?php if ($table_exists && !empty($deleted_records)): ?>
                        <table>
                            <thead>
                                <tr>
                                    <th>Registration Date</th>
                                    <th>Family Number</th>
                                    <th>Male Name</th>
                                    <th>Female Name</th>
                                    <th>Address</th>
                                    <th>Remarks</th>
                                    <th>Registered By</th>
                                    <th>Deletion Info</th>
                                    <th>Reason for Deletion</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($deleted_records as $record): ?>
                                    <tr data-id="<?php echo $record['id']; ?>">
                                        <td><?php echo date('M d, Y', strtotime($record['registration_date'])); ?></td>
                                        <td class="family-number"><?php echo htmlspecialchars($record['family_number']); ?></td>
                                        <td><?php echo htmlspecialchars($record['male_name']); ?></td>
                                        <td><?php echo htmlspecialchars($record['female_name']); ?></td>
                                        <td><?php echo htmlspecialchars($record['address']); ?></td>
                                        <td><?php echo htmlspecialchars($record['remarks'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars(strtoupper($record['registered_by'])); ?></td>
                                        <td class="deletion-info">
                                            <div>
                                                <span style="font-weight: 600;"><?php echo htmlspecialchars($record['deleted_by']); ?></span><br>
                                                <span style="font-size: 0.85em; color: #666;"><?php echo date('M d, Y g:i A', strtotime($record['deleted_at'])); ?></span>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($record['deletion_reason'] ?? ''); ?></td>
                                        <td class="action-buttons">
                                            <button type="button" class="action-btn delete-forever-btn" data-id="<?php echo $record['id']; ?>" title="Permanently Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?page=1<?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                    <a href="?page=<?php echo $page - 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                <?php endif; ?>
                                
                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);
                                
                                for ($i = $start_page; $i <= $end_page; $i++): ?>
                                    <a href="?page=<?php echo $i; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>" 
                                       class="<?php echo $i == $page ? 'active' : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <a href="?page=<?php echo $page + 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                    <a href="?page=<?php echo $total_pages; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?>">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                    <?php elseif ($table_exists): ?>
                        <div class="no-records">
                            <i class="fas fa-trash-alt"></i> No deleted records found.
                        </div>
                    <?php else: ?>
                        <div class="no-records">
                            <i class="fas fa-database"></i> Deleted records table not found.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal()">&times;</span>
            <div class="modal-header">
                <h3>Confirm Permanent Deletion</h3>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to permanently delete this record? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn cancel-btn">Cancel</button>
                <form method="POST" id="deleteForm">
                    <input type="hidden" name="permanent_delete" id="deleteId">
                    <button type="submit" class="btn delete-btn">Delete Permanently</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete All Confirmation Modal -->
    <div id="deleteAllModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Delete All Records</h3>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to permanently delete ALL records? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn cancel-btn" onclick="closeDeleteAllModal()">Cancel</button>
                <form method="POST">
                    <input type="hidden" name="delete_all" value="1">
                    <button type="submit" class="btn delete-btn">Delete All Permanently</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Timeout functionality
        let inactivityTimeout;
        
        function resetTimeout() {
            clearTimeout(inactivityTimeout);
            inactivityTimeout = setTimeout(function() {
                window.location.href = 'dashboard.php';
            }, 300000); // 5 minutes timeout
        }
        
        // Initialize timeout when page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetTimeout();
            
            // Reset timeout on user activity
            document.addEventListener('mousemove', resetTimeout);
            document.addEventListener('keypress', resetTimeout);
            document.addEventListener('click', resetTimeout);
            document.addEventListener('scroll', resetTimeout);
            
            // Add event listeners to form elements to reset timeout
            const formElements = document.querySelectorAll('input, select, textarea, button');
            formElements.forEach(element => {
                element.addEventListener('focus', resetTimeout);
                element.addEventListener('change', resetTimeout);
            });
        });
        
        // Variables for record IDs
        let restoreId = null;
        let deleteForeverId = null;
        
        // Declaring closeModal and closeDeleteAllModal in global scope
        function closeModal() {
            $('#deleteModal').hide();
            resetTimeout(); // Reset timeout when modal is closed
        }
        
        function closeDeleteAllModal() {
            $('#deleteAllModal').hide();
            resetTimeout(); // Reset timeout when modal is closed
        }
        
        function openRestoreModal(id) {
            restoreId = id;
            document.getElementById('restoreModal').style.display = 'block';
            resetTimeout(); // Reset timeout when opening modal
        }
        
        function openDeleteForeverModal(id) {
            deleteForeverId = id;
            document.getElementById('deleteId').value = id;
            document.getElementById('deleteModal').style.display = 'block';
            resetTimeout(); // Reset timeout when opening modal
        }
        
        $(document).ready(function() {
            // Delete modal functionality
            $('.delete-forever-btn').click(function() {
                var id = $(this).data('id');
                openDeleteForeverModal(id);
                resetTimeout(); // Reset timeout when opening modal
            });
            
            // Close modal when clicking X or Cancel
            $('.close-modal, .cancel-btn').click(function() {
                closeModal();
                resetTimeout(); // Reset timeout when closing modal
            });
            
            // Close modal when clicking outside
            $(window).click(function(e) {
                if ($(e.target).hasClass('modal')) {
                    closeModal();
                    closeDeleteAllModal();
                    resetTimeout(); // Reset timeout when closing modal
                }
            });
            
            // Initialize dropdown functionality for sidebar
            $('.dropdown-btn').click(function(e) {
                e.preventDefault();
                $(this).toggleClass('active');
                $(this).next('.dropdown-container').toggleClass('show');
                resetTimeout(); // Reset timeout when interacting with dropdown
            });
            
            // Animate table rows on page load
            $('tbody tr').each(function(index) {
                $(this).css({
                    'opacity': '0',
                    'transform': 'translateX(20px)'
                });
                
                setTimeout(() => {
                    $(this).css({
                        'opacity': '1',
                        'transform': 'translateX(0)',
                        'transition': 'all 0.3s ease'
                    });
                }, 100 + (index * 50));
            });
            
            // Add tooltips for truncated cells
            $('td').each(function() {
                // Skip the deletion info column (8th column)
                if ($(this).index() !== 7) {
                    if(this.offsetWidth < this.scrollWidth && !this.title) {
                        this.title = $(this).text();
                    }
                }
            });
            
            // Delete all records button functionality
            $('#deleteAllBtn').click(function() {
                $('#deleteAllModal').show();
                resetTimeout(); // Reset timeout when opening modal
            });
        });
    </script>

<?php
$conn->close();
?> 