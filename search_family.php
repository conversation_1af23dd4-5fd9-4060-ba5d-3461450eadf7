<?php
session_start();
if (!isset($_SESSION['username'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// Get search term
$search = isset($_GET['search']) ? $_GET['search'] : '';
$where_clause = '';

if (!empty($search)) {
    $search = $conn->real_escape_string($search);
    $where_clause = "WHERE 
        registration_date LIKE '%$search%' OR 
        family_number LIKE '%$search%' OR 
        male_name LIKE '%$search%' OR 
        female_name LIKE '%$search%' OR 
        address LIKE '%$search%' OR 
        remarks LIKE '%$search%'";
}

// Fetch records
$sql = "SELECT * FROM family_registration $where_clause ORDER BY registration_date DESC";
$result = $conn->query($sql);
$records = [];

if ($result && $result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $records[] = [
            'registration_date' => $row['registration_date'],
            'family_number' => htmlspecialchars($row['family_number']),
            'male_name' => htmlspecialchars($row['male_name']),
            'female_name' => htmlspecialchars($row['female_name']),
            'address' => htmlspecialchars($row['address']),
            'remarks' => htmlspecialchars($row['remarks']),
            'id' => $row['id']
        ];
    }
}

header('Content-Type: application/json');
echo json_encode(['records' => $records]);
$conn->close();
?> 