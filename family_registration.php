<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Function to get next family number
function getNextFamilyNumber($conn) {
    $sql = "SELECT family_number FROM family_registration WHERE family_number LIKE 'FN-25-%' ORDER BY CAST(SUBSTRING_INDEX(family_number, '-', -1) AS UNSIGNED) DESC LIMIT 1";
    $result = $conn->query($sql);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $lastNumber = intval(substr($row['family_number'], -4));
        $nextNumber = $lastNumber + 1;
    } else {
        $nextNumber = 1644; // Starting number if no records exist
    }

    return "FN-25-" . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Clean and unwrap the text data
    $registration_date = trim($_POST['registration_date']);
    $family_number = trim(strtoupper($_POST['family_number']));
    $male_name = trim(preg_replace('/\s+/', ' ', strtoupper($_POST['male_name'])));
    $female_name = trim(preg_replace('/\s+/', ' ', strtoupper($_POST['female_name'])));
    $address = trim(preg_replace('/\s+/', ' ', strtoupper($_POST['address'])));
    $remarks = trim(preg_replace('/\s+/', ' ', strtoupper($_POST['remarks'])));
    $registered_by = $_SESSION['username'];

    // Check for duplicate names before inserting
    $duplicate_found = false;
    $duplicate_message = "";

    if (!empty($male_name)) {
        $check_male_sql = "SELECT COUNT(*) as count FROM family_registration WHERE TRIM(male_name) = ?";
        $check_male_stmt = $conn->prepare($check_male_sql);
        $check_male_stmt->bind_param("s", $male_name);
        $check_male_stmt->execute();
        $check_male_result = $check_male_stmt->get_result();
        $male_count = $check_male_result->fetch_assoc()['count'];

        if ($male_count > 0) {
            $duplicate_found = true;
            $duplicate_message .= "Male name already exists in the database. ";
        }
        $check_male_stmt->close();
    }

    if (!empty($female_name)) {
        $check_female_sql = "SELECT COUNT(*) as count FROM family_registration WHERE TRIM(female_name) = ?";
        $check_female_stmt = $conn->prepare($check_female_sql);
        $check_female_stmt->bind_param("s", $female_name);
        $check_female_stmt->execute();
        $check_female_result = $check_female_stmt->get_result();
        $female_count = $check_female_result->fetch_assoc()['count'];

        if ($female_count > 0) {
            $duplicate_found = true;
            $duplicate_message .= "Female name already exists in the database.";
        }
        $check_female_stmt->close();
    }

    if ($duplicate_found) {
        $error_message = "Registration failed: " . $duplicate_message;
    } else {
    $sql = "INSERT INTO family_registration (registration_date, family_number, male_name, female_name, address, remarks, registered_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssss", $registration_date, $family_number, $male_name, $female_name, $address, $remarks, $registered_by);

    if ($stmt->execute()) {
        $success_message = "Family registration successful!";
        // Get the next family number after successful registration
        $nextFamilyNumber = getNextFamilyNumber($conn);
    } else {
        $error_message = "Error: " . $stmt->error;
    }

    $stmt->close();
    }
}

// Get initial family number
$nextFamilyNumber = getNextFamilyNumber($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Registration - La Huerta Health Center</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="./images/brgy_la_huerta.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./images/brgy_la_huerta.png">
    <link rel="shortcut icon" href="./images/brgy_la_huerta.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./images/brgy_la_huerta.png">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            min-height: 100vh;
            background: #f0f7ff;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }

        .dropdown-container.show {
            display: block;
        }

        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
            animation: scaleIn 0.5s ease-out forwards;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
        }

        .header {
            text-align: center;
            margin-bottom: 2.5rem;
            animation: fadeIn 0.6s ease-out forwards;
        }

        .header h2 {
            color: #000;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .header p {
            color: #000;
            font-size: 1rem;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 1.8rem;
            opacity: 0;
            animation: fadeIn 0.5s ease-out forwards;
        }

        .form-group:nth-child(1) { animation-delay: 0.2s; }
        .form-group:nth-child(2) { animation-delay: 0.3s; }
        .form-group:nth-child(3) { animation-delay: 0.4s; }
        .form-group:nth-child(4) { animation-delay: 0.5s; }
        .form-group:nth-child(5) { animation-delay: 0.6s; }
        .form-group:nth-child(6) { animation-delay: 0.7s; }

        .form-group label {
            display: block;
            margin-bottom: 0.8rem;
            color: #000;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #555;
            font-size: 1.1rem;
            z-index: 2;
            pointer-events: none;
            opacity: 0.8;
        }

        /* Calendar icon specific */
        .input-group .fa-calendar,
        .input-group .fa-hashtag {
            color: #4a5568;
        }

        /* Gender icons specific */
        .input-group .fa-mars {
            color: #3182ce;
        }

        .input-group .fa-venus {
            color: #d53f8c;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
            color: #333;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        /* Family number field styling */
        #family_number {
            background: #f0f0f0;
            font-weight: 600;
            letter-spacing: 0.5px;
            color: #333;
            font-size: 1.1rem;
            transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
            padding-right: 3rem;
            cursor: default;
        }

        /* Animation for the family number value */
        .highlight-value {
            background-color: #e9ecef !important;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            transform: scale(1.02);
            letter-spacing: 1px;
        }

        /* Textarea special styling for address and remarks */
        .form-group textarea {
            padding: 1rem 1rem 1rem 3rem;
            height: 120px;
            resize: vertical;
        }

        /* Ensure the map-marker and comment icons stay at the top */
        .form-group:nth-child(5) .input-group i,
        .form-group:nth-child(6) .input-group i {
            top: 1.5rem;
            transform: none;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4db3a8;
            background: white;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        /* Animation styles */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.9);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes shimmer {
            100% {
                left: 100%;
            }
        }

        .message {
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            color: #000;
            animation: scaleIn 0.4s ease-out forwards;
        }

        .success {
            background: #f0fff4;
            border: 1px solid #c8f7d4;
        }

        .error {
            background: #fff5f5;
            border: 1px solid #fde8e8;
            border-left: 4px solid #e53e3e;
        }

        .error i {
            color: #e53e3e;
        }

        .message i {
            font-size: 1.2rem;
        }

        .submit-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .submit-btn::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        .back-link {
            display: inline-block;
            margin-top: 1.5rem;
            color: #000;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: #3a8f87;
        }

        /* Notification styles for duplicate names */
        .name-notification {
            background-color: #fff3cd;
            color: #856404;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            margin-top: 8px;
            border-left: 3px solid #ffc107;
            display: flex;
            align-items: flex-start;
            gap: 8px;
            line-height: 1.3;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .name-notification i {
            color: #ffc107;
            font-size: 1rem;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .input-group {
            position: relative;
        }

        .duplicate-warning {
            border-color: #ffc107 !important;
            background-color: #fffdf0 !important;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }

            .main-content {
                padding: 1rem;
            }

            .container {
                padding: 2rem;
            }

            .header h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link active">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link dropdown-btn">
                        <i class="fas fa-folder"></i>
                        Records
                    </a>
                    <div class="dropdown-container">
                        <a href="view_family_records.php" class="dropdown-link">
                            <i class="fas fa-table"></i>
                            View Records
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                            <i class="fas fa-exclamation-triangle"></i>
                            Duplicate Records
                        </a>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <a href="missing_family_numbers.php" class="dropdown-link">
                            <i class="fas fa-list-ol"></i>
                            Missing Family Numbers
                        </a>
                        <a href="recently_deleted.php" class="dropdown-link">
                            <i class="fas fa-trash-alt"></i>
                            Recently Deleted
                        </a>
                        <?php endif; ?>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>
        <div class="main-content">
            <div class="container">
                <div class="header">
                    <h2>Family Number Registration</h2>
                    <p>Please fill in the family details below</p>
                </div>

                <?php if (isset($success_message)): ?>
                    <div class="message success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="message error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <div class="form-group">
                        <label for="registration_date">Registration Date</label>
                        <div class="input-group">
                            <i class="fas fa-calendar"></i>
                            <input type="date" id="registration_date" name="registration_date" required value="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="family_number">Family Number</label>
                        <div class="input-group">
                            <i class="fas fa-hashtag"></i>
                            <?php if ($_SESSION['role'] === 'super admin'): ?>
                            <input type="text" id="family_number" name="family_number" required value="<?php echo htmlspecialchars($nextFamilyNumber); ?>">
                            <?php else: ?>
                            <input type="text" id="family_number" name="family_number" required value="<?php echo htmlspecialchars($nextFamilyNumber); ?>" readonly>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="male_name">Male Name</label>
                        <div class="input-group">
                            <i class="fas fa-mars"></i>
                            <input type="text" id="male_name" name="male_name" placeholder="Enter male name">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="female_name">Female Name</label>
                        <div class="input-group">
                            <i class="fas fa-venus"></i>
                            <input type="text" id="female_name" name="female_name" placeholder="Enter female name">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address">Address <span style="color: red;">*</span></label>
                        <div class="input-group">
                            <i class="fas fa-map-marker-alt"></i>
                            <textarea id="address" name="address" required placeholder="Enter complete address (required)"></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="remarks">Remarks</label>
                        <div class="input-group">
                            <i class="fas fa-comment"></i>
                            <textarea id="remarks" name="remarks" placeholder="Enter any additional remarks"></textarea>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save"></i>
                        Register Family
                    </button>
                </form>
                <br> </br>
                <footer style="margin-top: -0px; padding: 10px; text-align: center; border-top: 1px solid #ddd; font-size: 13px; color: #666;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <span>&copy; <?php echo date("Y"); ?> Family Number Management System</span>
        <span style="margin-right: 10px;">HIMU-Justin</span>
    </div>
</footer>
            </div>
        </div>
    </div>
    <script>
        flatpickr("#registration_date", {
            dateFormat: "Y-m-d",
            defaultDate: "today",
            altInput: true,
            altFormat: "F j, Y",
            enableTime: false,
            disableMobile: true,
            allowInput: true
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Fix for flatpickr accessibility - connect label to visible input
            setTimeout(() => {
                const flatpickrInput = document.querySelector(".flatpickr-input[type=text]");
                if (flatpickrInput) {
                    flatpickrInput.id = "registration_date_display";
                    document.querySelector('label[for="registration_date"]').setAttribute('for', 'registration_date_display');
                }
            }, 100);

            // Set initial family number
            const familyNumberInput = document.getElementById('family_number');
            familyNumberInput.value = '<?php echo htmlspecialchars($nextFamilyNumber); ?>';

            // Enhanced animation for family number
            function animateFamilyNumber() {
                familyNumberInput.classList.add('highlight-value');
                setTimeout(() => {
                    familyNumberInput.classList.remove('highlight-value');
                    setTimeout(() => {
                        // Repeat animation once more
                        familyNumberInput.classList.add('highlight-value');
                        setTimeout(() => {
                            familyNumberInput.classList.remove('highlight-value');
                        }, 700);
                    }, 300);
                }, 700);
            }

            // Run the animation
            setTimeout(animateFamilyNumber, 500);

            // Initialize dropdown functionality for sidebar
            const dropdownBtns = document.querySelectorAll('.dropdown-btn');

            dropdownBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.classList.toggle('active');
                    const dropdownContent = this.nextElementSibling;
                    dropdownContent.classList.toggle('show');
                });
            });

            // Add subtle animations to form inputs
            const formInputs = document.querySelectorAll('input, textarea');
            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'translateX(5px)';
                    setTimeout(() => {
                        this.style.transform = 'translateX(0)';
                    }, 300);
                });
            });

            // Add animated hover effect to submit button
            const submitBtn = document.querySelector('.submit-btn');
            submitBtn.addEventListener('mouseover', function() {
                this.style.transform = 'translateY(-5px)';
            });
            submitBtn.addEventListener('mouseout', function() {
                this.style.transform = 'translateY(0)';
            });

            // Add event listeners for name checks
            const maleNameInput = document.getElementById('male_name');
            const femaleNameInput = document.getElementById('female_name');
            const form = document.querySelector('form');

            let maleNameTimeout;
            let femaleNameTimeout;
            let maleNameDuplicate = false;
            let femaleNameDuplicate = false;

            // Check male name
            maleNameInput.addEventListener('input', function() {
                clearTimeout(maleNameTimeout);

                const maleName = this.value.trim();
                if (maleName.length < 3) {
                    removeMaleNameNotification();
                    maleNameDuplicate = false;
                    return;
                }

                maleNameTimeout = setTimeout(function() {
                    checkNameExists('male', maleName);
                }, 500);
            });

            // Check female name
            femaleNameInput.addEventListener('input', function() {
                clearTimeout(femaleNameTimeout);

                const femaleName = this.value.trim();
                if (femaleName.length < 3) {
                    removeFemaleNameNotification();
                    femaleNameDuplicate = false;
                    return;
                }

                femaleNameTimeout = setTimeout(function() {
                    checkNameExists('female', femaleName);
                }, 500);
            });

            // Function to check if name exists
            function checkNameExists(type, name) {
                fetch('check_name_exists.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'type=' + type + '&name=' + encodeURIComponent(name)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.exists) {
                        if (type === 'male') {
                            maleNameDuplicate = true;
                            showMaleNameNotification(data.count, data.familyNumbers);
                        } else {
                            femaleNameDuplicate = true;
                            showFemaleNameNotification(data.count, data.familyNumbers);
                        }
                    } else {
                        if (type === 'male') {
                            maleNameDuplicate = false;
                            removeMaleNameNotification();
                        } else {
                            femaleNameDuplicate = false;
                            removeFemaleNameNotification();
                        }
                    }
                })
                .catch(error => {
                    console.error('Error checking name:', error);
                });
            }

            // Function to show male name notification
            function showMaleNameNotification(count, familyNumbers) {
                removeMaleNameNotification();

                const notification = document.createElement('div');
                notification.className = 'name-notification';
                notification.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    This male name appears in ${count} existing record${count !== 1 ? 's' : ''}: ${familyNumbers.join(', ')}
                    <br><strong>Registration with duplicate names is not allowed.</strong>
                `;

                maleNameInput.parentNode.appendChild(notification);
                maleNameInput.classList.add('duplicate-warning');
            }

            // Function to show female name notification
            function showFemaleNameNotification(count, familyNumbers) {
                removeFemaleNameNotification();

                const notification = document.createElement('div');
                notification.className = 'name-notification';
                notification.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    This female name appears in ${count} existing record${count !== 1 ? 's' : ''}: ${familyNumbers.join(', ')}
                    <br><strong>Registration with duplicate names is not allowed.</strong>
                `;

                femaleNameInput.parentNode.appendChild(notification);
                femaleNameInput.classList.add('duplicate-warning');
            }

            // Function to remove male name notification
            function removeMaleNameNotification() {
                const existingNotification = maleNameInput.parentNode.querySelector('.name-notification');
                if (existingNotification) {
                    existingNotification.remove();
                }
                maleNameInput.classList.remove('duplicate-warning');
            }

            // Function to remove female name notification
            function removeFemaleNameNotification() {
                const existingNotification = femaleNameInput.parentNode.querySelector('.name-notification');
                if (existingNotification) {
                    existingNotification.remove();
                }
                femaleNameInput.classList.remove('duplicate-warning');
            }

            // Prevent form submission if duplicates exist
            form.addEventListener('submit', function(e) {
                const maleName = maleNameInput.value.trim();
                const femaleName = femaleNameInput.value.trim();

                // If either name field has a duplicate and is not empty, prevent submission
                if ((maleName !== '' && maleNameDuplicate) || (femaleName !== '' && femaleNameDuplicate)) {
                    e.preventDefault();

                    // Show error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'message error';
                    errorDiv.innerHTML = `
                        <i class="fas fa-exclamation-circle"></i>
                        Registration failed: Duplicate names are not allowed.
                    `;

                    // Remove any existing error messages
                    const existingErrors = document.querySelectorAll('.message.error');
                    existingErrors.forEach(error => error.remove());

                    // Insert error message at the top of the form
                    form.insertAdjacentElement('beforebegin', errorDiv);

                    // Scroll to the top of the form
                    window.scrollTo({
                        top: form.offsetTop - 100,
                        behavior: 'smooth'
                    });

                    return false;
                }

            // Clear form fields after successful submission
                if (document.querySelector('.message.success')) {
                    setTimeout(() => {
                        document.getElementById('male_name').value = '';
                        document.getElementById('female_name').value = '';
                        document.getElementById('address').value = '';
                        document.getElementById('remarks').value = '';
                        document.getElementById('family_number').value = '<?php echo htmlspecialchars($nextFamilyNumber); ?>';
                    }, 100);
                }
            });

            // Timeout functionality
            let inactivityTimeout;

            function resetTimeout() {
                clearTimeout(inactivityTimeout);
                inactivityTimeout = setTimeout(function() {
                    window.location.href = 'dashboard.php';
                }, 3000); // 3 seconds timeout
            }

            // Initialize timeout when page loads
            document.addEventListener('DOMContentLoaded', function() {
                resetTimeout();

                // Reset timeout on user activity
                document.addEventListener('mousemove', resetTimeout);
                document.addEventListener('keypress', resetTimeout);
                document.addEventListener('click', resetTimeout);
                document.addEventListener('scroll', resetTimeout);

                // Add event listeners to form elements to reset timeout
                const formElements = document.querySelectorAll('input, select, textarea, button');
                formElements.forEach(element => {
                    element.addEventListener('focus', resetTimeout);
                    element.addEventListener('change', resetTimeout);
                });
            });
        });
    </script>
</body>
</html>
<?php $conn->close(); ?>
