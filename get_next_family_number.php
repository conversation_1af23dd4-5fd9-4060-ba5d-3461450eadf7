<?php
session_start();
require 'db.php';

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['error' => 'Not logged in']);
    exit();
}

// Get the highest family number
$sql = "SELECT family_number FROM family_registration WHERE family_number LIKE 'FN-25-%' ORDER BY CAST(SUBSTRING(family_number, 7) AS UNSIGNED) DESC LIMIT 1";
$result = $conn->query($sql);

$next_number = 1644; // Starting number if no records exist

if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $current_number = (int)substr($row['family_number'], 6); // Extract the number part
    $next_number = $current_number + 1;
}

// Return the next number
echo json_encode(['next_number' => $next_number]);

$conn->close();
?> 