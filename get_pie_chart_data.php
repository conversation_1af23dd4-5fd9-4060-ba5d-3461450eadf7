<?php
session_start();
if (!isset($_SESSION['username'])) {
    http_response_code(403);
    exit('Unauthorized');
}

include "db.php";

// Function to generate rainbow colors
function generateRainbowColors($count) {
    $colors = [];
    if ($count === 0) return $colors;

    for ($i = 0; $i < $count; $i++) {
        // Generate hue across the full spectrum (0-360 degrees)
        $hue = ($i * 360) / $count;

        // Use high saturation and medium lightness for vibrant colors
        $saturation = 85; // 85% saturation for vibrant colors
        $lightness = 50;  // 50% lightness for good contrast

        // Convert HSL to RGB
        $rgb = hslToRgb($hue, $saturation, $lightness);
        $colors[] = sprintf('#%02x%02x%02x', $rgb[0], $rgb[1], $rgb[2]);
    }

    return $colors;
}

// Function to convert HSL to RGB
function hslToRgb($h, $s, $l) {
    $h = $h / 360;
    $s = $s / 100;
    $l = $l / 100;

    if ($s == 0) {
        $r = $g = $b = $l; // achromatic
    } else {
        $hue2rgb = function($p, $q, $t) {
            if ($t < 0) $t += 1;
            if ($t > 1) $t -= 1;
            if ($t < 1/6) return $p + ($q - $p) * 6 * $t;
            if ($t < 1/2) return $q;
            if ($t < 2/3) return $p + ($q - $p) * (2/3 - $t) * 6;
            return $p;
        };

        $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
        $p = 2 * $l - $q;
        $r = $hue2rgb($p, $q, $h + 1/3);
        $g = $hue2rgb($p, $q, $h);
        $b = $hue2rgb($p, $q, $h - 1/3);
    }

    return [round($r * 255), round($g * 255), round($b * 255)];
}

// Initialize the response data
$data = [
    'labels' => [],
    'datasets' => [
        [
            'data' => [],
            'backgroundColor' => [],
            'borderColor' => '#ffffff',
            'borderWidth' => 2
        ]
    ]
];

// Get period filter from request
$period = isset($_GET['period']) ? $_GET['period'] : 'all';

// Build date filter based on period
$dateFilter = '';
switch ($period) {
    case 'week':
        $dateFilter = "AND registration_date >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)";
        break;
    case 'month':
        $dateFilter = "AND registration_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
        break;
    case 'year':
        $dateFilter = "AND registration_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)";
        break;
    default:
        // All time - no filter
        $dateFilter = "";
}

// Get the count of registrations by each user
$sql = "SELECT registered_by, COUNT(*) as count
        FROM family_registration
        WHERE registered_by IS NOT NULL AND registered_by != ''
        $dateFilter
        GROUP BY registered_by
        ORDER BY count DESC";

$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // Format the user's name properly
        $username = ucwords(strtolower($row['registered_by']));
        $count = (int)$row['count'];

        // Add to the data arrays
        $data['labels'][] = $username;
        $data['datasets'][0]['data'][] = $count;
    }

    // Generate rainbow colors based on the number of users
    $rainbowColors = generateRainbowColors(count($data['labels']));
    $data['datasets'][0]['backgroundColor'] = $rainbowColors;
}

// Add period info to the response
$data['period'] = $period;
$periodLabels = [
    'all' => 'All Time',
    'year' => 'This Year',
    'month' => 'This Month',
    'week' => 'This Week'
];
$data['periodLabel'] = $periodLabels[$period];

// Return JSON data
header('Content-Type: application/json');
echo json_encode($data);

$conn->close();
?>