<?php
session_start();
include "db.php";

// Ensure the user is logged in and has super admin role
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'super admin') {
    header("Location: dashboard.php");
    exit();
}

// Check if import_batches table exists, create it if not
$check_batch_table = $conn->query("SHOW TABLES LIKE 'import_batches'");
if ($check_batch_table->num_rows == 0) {
    // Table doesn't exist, create it
    $create_table_sql = "CREATE TABLE import_batches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        batch_identifier VARCHAR(255) NOT NULL,
        import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        imported_by VARCHAR(255) NOT NULL,
        record_count INT NOT NULL,
        has_been_undone BOOLEAN DEFAULT FALSE
    )";
    
    if (!$conn->query($create_table_sql)) {
        $_SESSION['error'] = "Failed to create import tracking table: " . $conn->error;
        header("Location: dashboard.php");
        exit();
    }
}

// Check if the family_registration table has a batch_identifier column, add it if not
$check_batch_column = $conn->query("SHOW COLUMNS FROM family_registration LIKE 'batch_identifier'");
if ($check_batch_column->num_rows == 0) {
    // Column doesn't exist, add it
    $add_column_sql = "ALTER TABLE family_registration ADD COLUMN batch_identifier VARCHAR(255) NULL";
    
    if (!$conn->query($add_column_sql)) {
        $_SESSION['error'] = "Failed to add batch tracking to family records: " . $conn->error;
        header("Location: dashboard.php");
        exit();
    }
}

// Get the most recent import batch that hasn't been undone
$get_batch_sql = "SELECT * FROM import_batches WHERE has_been_undone = FALSE ORDER BY import_date DESC LIMIT 1";
$batch_result = $conn->query($get_batch_sql);

if ($batch_result->num_rows == 0) {
    // No batches found, nothing to undo
    $_SESSION['warning'] = "No recent imports found to undo.";
    header("Location: dashboard.php");
    exit();
}

$batch = $batch_result->fetch_assoc();
$batch_id = $batch['id'];
$batch_identifier = $batch['batch_identifier'];
$record_count = $batch['record_count'];

// Begin transaction for data integrity
$conn->begin_transaction();

try {
    // Get the records that will be deleted (for logging purposes)
    $get_records_sql = "SELECT * FROM family_registration WHERE batch_identifier = ?";
    $records_stmt = $conn->prepare($get_records_sql);
    $records_stmt->bind_param("s", $batch_identifier);
    $records_stmt->execute();
    $records_result = $records_stmt->get_result();
    
    $records_to_delete = [];
    while ($row = $records_result->fetch_assoc()) {
        $records_to_delete[] = $row;
    }
    
    // Delete the records from this batch
    $delete_sql = "DELETE FROM family_registration WHERE batch_identifier = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("s", $batch_identifier);
    $delete_stmt->execute();
    
    $deleted_count = $delete_stmt->affected_rows;
    
    // Mark the batch as undone
    $update_batch_sql = "UPDATE import_batches SET has_been_undone = TRUE WHERE id = ?";
    $update_stmt = $conn->prepare($update_batch_sql);
    $update_stmt->bind_param("i", $batch_id);
    $update_stmt->execute();
    
    // Optional: Log the undo operation
    $log_undo_sql = "INSERT INTO system_log (action, performed_by, details) VALUES (?, ?, ?)";
    
    // Check if system_log table exists
    $check_log_table = $conn->query("SHOW TABLES LIKE 'system_log'");
    if ($check_log_table->num_rows > 0) {
        $log_stmt = $conn->prepare($log_undo_sql);
        $action = "UNDO_IMPORT";
        $performed_by = $_SESSION['username'];
        $details = json_encode([
            'batch_identifier' => $batch_identifier,
            'record_count' => $record_count,
            'actual_deleted' => $deleted_count,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $log_stmt->bind_param("sss", $action, $performed_by, $details);
        $log_stmt->execute();
    }
    
    // Commit the transaction
    $conn->commit();
    
    $_SESSION['success'] = "Successfully undone the last import. {$deleted_count} records were removed.";
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    $_SESSION['error'] = "Error undoing import: " . $e->getMessage();
}

// Redirect back to dashboard
header("Location: dashboard.php");
exit();
?> 