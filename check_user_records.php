<?php
session_start();
include "db.php";

// Check if user is logged in and is an admin
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'super admin')) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Check if user_id is provided
if (!isset($_GET['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'No user ID provided']);
    exit();
}

$user_id = $_GET['user_id'];

// Get the username for the user
$username_sql = "SELECT username, role FROM users WHERE id = ?";
$username_stmt = $conn->prepare($username_sql);
$username_stmt->bind_param("i", $user_id);
$username_stmt->execute();
$result = $username_stmt->get_result();

if ($result->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'User not found']);
    exit();
}

$user_data = $result->fetch_assoc();
$username = $user_data['username'];
$role = $user_data['role'];

// Check if user has any family records
$records_sql = "SELECT COUNT(*) as count FROM family_registration WHERE registered_by = ?";
$records_stmt = $conn->prepare($records_sql);
$records_stmt->bind_param("s", $username);
$records_stmt->execute();
$count = $records_stmt->get_result()->fetch_assoc()['count'];

header('Content-Type: application/json');
echo json_encode([
    'has_records' => $count > 0,
    'record_count' => $count,
    'role' => $role
]);

$conn->close();
?> 