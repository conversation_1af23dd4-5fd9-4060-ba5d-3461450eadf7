<?php
session_start();
if (!isset($_SESSION['username'])) {
    http_response_code(403);
    exit('Unauthorized');
}

include "db.php";

$period = isset($_GET['period']) ? $_GET['period'] : 'day';
$data = ['labels' => [], 'datasets' => []];

// Get all users who have registered families
$users_sql = "SELECT DISTINCT registered_by FROM family_registration WHERE registered_by IS NOT NULL AND registered_by != '' ORDER BY registered_by ASC";
$users_result = $conn->query($users_sql);
$users = [];
while ($row = $users_result->fetch_assoc()) {
    $users[] = $row['registered_by'];
}

// Set the current date as the end date and adjust to include the current year fully
$end_date = new DateTime();
$end_date->setTime(23, 59, 59); // Set to end of day

if ($period === 'year') {
    // For yearly view, set end date to December 31 of the current year
    $end_date->setDate($end_date->format('Y'), 12, 31);
}
$interval = new DateInterval('P1D'); // Default to daily

switch ($period) {
    case 'week':
        $interval = new DateInterval('P1W');
        $start_date = (clone $end_date)->modify('first day of this week')->modify('-5 weeks');
        $end_date->modify('last day of this week');
        break;
    case 'month':
        $interval = new DateInterval('P1M');
        $start_date = (clone $end_date)->modify('first day of this month')->modify('-5 months');
        $end_date->modify('last day of this month');
        break;
    case 'year':
        $interval = new DateInterval('P1Y');
        // Adjust to show 6 years including the current one
        $current_year = (int)$end_date->format('Y');
        $start_date = new DateTime();
        $start_date->setDate($current_year - 5, 1, 1); // Start from January 1, 5 years ago
        break;
    default: // day
        $start_date = (clone $end_date)->modify('-4 days'); // Show last 5 days including today
}

// Generate labels based on period
$labels = [];
$dates = [];
$date = clone $start_date;

// If year period, ensure we have one label per year
if ($period === 'year') {
    $years_to_show = [];
    $current_year = (int)$end_date->format('Y');
    for ($i = $current_year - 5; $i <= $current_year; $i++) {
        $years_to_show[] = $i;
        $year_date = new DateTime();
        $year_date->setDate($i, 1, 1);
        $dates[] = clone $year_date;
        $labels[] = (string)$i;
    }
    $data['labels'] = $labels;
} else {
    // For non-year periods, use the original logic
    while ($date <= $end_date) {
        switch ($period) {
            case 'week':
                $labels[] = $date->format('M d');
                break;
            case 'month':
                $labels[] = $date->format('M Y');
                break;
            default:
                $labels[] = $date->format('M d');
        }
        $dates[] = clone $date;
        $date->add($interval);
    }
    $data['labels'] = $labels;
}

// Generate datasets for each user
$colors = [
    '#4db3a8', '#3498db', '#e74c3c', '#f1c40f', '#9b59b6', 
    '#1abc9c', '#e67e22', '#34495e', '#7f8c8d', '#16a085'
];

// Add individual user datasets
foreach ($users as $index => $user) {
    $dataset = [
        'label' => ucwords(strtolower($user)),
        'data' => [],
        'backgroundColor' => $colors[$index % count($colors)],
        'borderColor' => $colors[$index % count($colors)],
        'borderWidth' => 1,
        'fill' => false
    ];

    foreach ($dates as $i => $date) {
        if ($period === 'year') {
            // For yearly data, get full year stats
            $year = (int)$labels[$i];
            $start_of_year = sprintf('%d-01-01', $year);
            $end_of_year = sprintf('%d-12-31', $year);
            
            $sql = "SELECT COUNT(*) as count FROM family_registration 
                    WHERE registered_by = ? 
                    AND registration_date >= ? 
                    AND registration_date <= ?";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('sss', $user, $start_of_year, $end_of_year);
        } else {
            // For other periods, use the modified range logic
            $period_start = clone $date;
            $period_end = clone $date;
            
            switch ($period) {
                case 'month':
                    $period_start->modify('first day of this month');
                    $period_end->modify('last day of this month');
                    break;
                case 'week':
                    $period_start->modify('monday this week');
                    $period_end->modify('sunday this week');
                    break;
                default: // day
                    $period_end->modify('+1 day');
                    break;
            }
            
            $sql = "SELECT COUNT(*) as count FROM family_registration 
                    WHERE registered_by = ? 
                    AND registration_date >= ? 
                    AND registration_date < ?";
            
            $stmt = $conn->prepare($sql);
            $start_str = $period_start->format('Y-m-d');
            $end_str = $period_end->format('Y-m-d');
            $stmt->bind_param('sss', $user, $start_str, $end_str);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc()['count'];
        
        $dataset['data'][] = $count;
    }

    $data['datasets'][] = $dataset;
}

header('Content-Type: application/json');
echo json_encode($data);

$conn->close();
?> 