<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'super admin') {
    header("Location: dashboard.php");
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get all existing family numbers from the database
$sql = "SELECT family_number FROM family_registration
        WHERE family_number LIKE 'FN-%-%'
        ORDER BY
        SUBSTRING_INDEX(family_number, '-', 1),
        CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(family_number, '-', 2), '-', -1) AS UNSIGNED),
        CAST(SUBSTRING_INDEX(family_number, '-', -1) AS UNSIGNED)";

$result = $conn->query($sql);
$existingNumbers = array();

if ($result && $result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $existingNumbers[] = $row['family_number'];
    }
}

// Process family numbers to find missing sequences
$missingNumbers = [];
$groups = [];
$years = [];

if (!empty($existingNumbers)) {
    // Group by year (middle segment)
    foreach ($existingNumbers as $number) {
        if (preg_match('/FN-(\d+)-(\d+)/', $number, $matches)) {
            $year = $matches[1];
            $num = (int)$matches[2];
            if (!isset($groups[$year])) {
                $groups[$year] = [];
                $years[] = $year; // Keep track of all years that exist
            }
            $groups[$year][] = $num;
        }
    }

    // Sort years numerically
    sort($years);

    // Sort numbers in each year group and find gaps
    foreach ($groups as $year => $numbers) {
        sort($numbers);

        // Find the min and max for this year
        $min = $numbers[0];
        $max = end($numbers);

        // Check for gaps within the sequence
        $foundGaps = false;
        for ($i = $min; $i <= $max; $i++) {
            if (!in_array($i, $numbers)) {
                $paddedNum = str_pad($i, 3, '0', STR_PAD_LEFT);
                $missingNumbers[$year][] = "FN-$year-$paddedNum";
                $foundGaps = true;
            }
        }

        // If no gaps found, set an empty array (we'll check this later to show a message)
        if (!$foundGaps) {
            $missingNumbers[$year] = [];
        }
    }
}

// Calculate totals for stats
$totalExisting = count($existingNumbers);
$totalMissing = 0;
foreach ($missingNumbers as $yearGroup) {
    $totalMissing += count($yearGroup);
}
$totalNumbers = $totalExisting + $totalMissing;
$coverage = $totalNumbers > 0 ? ($totalExisting / $totalNumbers) * 100 : 0;

// Ensure we always have sections for years 24 and 25, even if they don't exist in data
if (!in_array('24', $years)) {
    $missingNumbers['24'] = [];
}
if (!in_array('25', $years)) {
    $missingNumbers['25'] = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Missing Family Numbers - La Huerta Health Center</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="./images/brgy_la_huerta.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./images/brgy_la_huerta.png">
    <link rel="shortcut icon" href="./images/brgy_la_huerta.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./images/brgy_la_huerta.png">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            min-height: 100vh;
            background: #f0f7ff;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }

        .dropdown-container.show {
            display: block;
        }

        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
        }

        .header {
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #666;
        }

        .results-container {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .results-header h2 {
            font-size: 1.5rem;
            color: #333;
        }

        .missing-numbers {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            max-height: 500px;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .missing-number {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 5px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stats {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            display: flex;
            gap: 2rem;
        }

        .stat {
            flex: 1;
            text-align: center;
        }

        .stat h3 {
            font-size: 2rem;
            color: #4db3a8;
            margin-bottom: 0.5rem;
        }

        .stat p {
            color: #666;
            font-size: 0.9rem;
        }

        .year-section {
            margin-top: 2rem;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }

        .year-header {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .empty-message {
            text-align: center;
            padding: 2rem;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
            .missing-numbers {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
            .stats {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link dropdown-btn active">
                        <i class="fas fa-folder"></i>
                        Records
                    </a>
                    <div class="dropdown-container show">
                        <a href="view_family_records.php" class="dropdown-link">
                            <i class="fas fa-table"></i>
                            View Records
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                            <i class="fas fa-exclamation-triangle"></i>
                            Duplicate Records
                        </a>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <a href="missing_family_numbers.php" class="dropdown-link active">
                            <i class="fas fa-list-ol"></i>
                            Missing Family Numbers
                        </a>
                        <a href="recently_deleted.php" class="dropdown-link">
                            <i class="fas fa-trash-alt"></i>
                            Recently Deleted
                        </a>
                        <?php endif; ?>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                </li>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-content">
            <div class="header">
                <h1>Missing Family Numbers</h1>
                <p>View missing family numbers based on existing records</p>
            </div>

            <div class="results-container">
                <div class="results-header">
                    <h2>Missing Family Numbers</h2>
                    <span>Total: <?php echo $totalMissing; ?></span>
                </div>

                <div class="stats">
                    <div class="stat">
                        <h3><?php echo $totalNumbers; ?></h3>
                        <p>Total Numbers in Sequence</p>
                    </div>
                    <div class="stat">
                        <h3><?php echo $totalExisting; ?></h3>
                        <p>Existing Numbers</p>
                    </div>
                    <div class="stat">
                        <h3><?php echo $totalMissing; ?></h3>
                        <p>Missing Numbers</p>
                    </div>
                    <div class="stat">
                        <h3><?php echo number_format($coverage, 1); ?>%</h3>
                        <p>Coverage</p>
                    </div>
                </div>

                <?php if (empty($missingNumbers)): ?>
                    <div class="empty-message">
                        <p>No missing family numbers found in the sequence!</p>
                    </div>
                <?php else: ?>
                    <?php
                    // Process years in order, ensuring 24 and 25 are included
                    $orderedYears = array_unique(array_merge(['24', '25'], array_keys($missingNumbers)));
                    sort($orderedYears);

                    foreach ($orderedYears as $year):
                        // Get the missing numbers for this year, or empty array if none
                        $yearMissing = isset($missingNumbers[$year]) ? $missingNumbers[$year] : [];
                    ?>
                        <div class="year-section">
                            <div class="year-header">
                                <span>Year: FN-<?php echo $year; ?>-XXX</span>
                                <span>Missing: <?php echo count($yearMissing); ?></span>
                            </div>
                            <?php if (empty($yearMissing)): ?>
                                <div class="empty-message" style="padding: 1.5rem; text-align: center;">
                                    <p>No missing family numbers for FN-<?php echo $year; ?>-XXX</p>
                                </div>
                            <?php else: ?>
                                <div class="missing-numbers">
                                    <?php foreach($yearMissing as $number): ?>
                                        <div class="missing-number"><?php echo htmlspecialchars($number); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Timeout functionality
        let inactivityTimeout;

        function resetTimeout() {
            clearTimeout(inactivityTimeout);
            inactivityTimeout = setTimeout(function() {
                window.location.href = 'dashboard.php';
            }, 300000); // 5 minutes timeout
        }

        // Initialize timeout when page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetTimeout();

            // Reset timeout on user activity
            document.addEventListener('mousemove', resetTimeout);
            document.addEventListener('keypress', resetTimeout);
            document.addEventListener('click', resetTimeout);
            document.addEventListener('scroll', resetTimeout);

            // Add event listeners to form elements to reset timeout
            const formElements = document.querySelectorAll('input, select, textarea, button');
            formElements.forEach(element => {
                element.addEventListener('focus', resetTimeout);
                element.addEventListener('change', resetTimeout);
            });

            // Animation for stats
            const stats = document.querySelectorAll('.stat h3');
            stats.forEach(stat => {
                const value = parseFloat(stat.textContent);
                stat.textContent = '0';
                let current = 0;
                const increment = value / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= value) {
                        clearInterval(timer);
                        stat.textContent = stat.textContent.includes('%') ?
                            value.toFixed(1) + '%' :
                            value % 1 === 0 ?
                                Math.round(value) :
                                value.toFixed(1);
                    } else {
                        stat.textContent = Math.round(current);
                    }
                }, 10);
            });
        });

        // Initialize dropdown functionality for sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownBtns = document.querySelectorAll('.dropdown-btn');

            dropdownBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.classList.toggle('active');
                    const dropdownContent = this.nextElementSibling;
                    dropdownContent.classList.toggle('show');
                    resetTimeout(); // Reset timeout when interacting with dropdown
                });
            });
        });
    </script>
</body>
</html>
<?php $conn->close(); ?>