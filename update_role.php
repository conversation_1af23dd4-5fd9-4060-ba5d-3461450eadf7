<?php
session_start();
include "db.php";

// Check if user is logged in and is an admin
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'super admin')) {
    header("Location: dashboard.php");
    exit();
}

// Update <PERSON>'s role to super admin
$sql = "UPDATE users SET role = 'super admin' WHERE username = 'justin'";
if ($conn->query($sql) === TRUE) {
    echo "Role updated successfully!";
} else {
    echo "Error updating role: " . $conn->error;
}

$conn->close();
?> 