<?php
session_start();
include "db.php";

if (!isset($_GET['data'])) {
    header("Location: scanner.php");
    exit();
}

// Decode the data if it's base64 encoded (from QR code)
$data = $_GET['data'];
$family_number = $data;

// Try to decode if it looks like base64
if (base64_encode(base64_decode($data, true)) === $data) {
    $decoded = base64_decode($data);
    if ($decoded !== false) {
        $family_number = $decoded;
    }
}

// Clean the family number
$family_number = trim($family_number);

// Query to get family record
$sql = "SELECT * FROM family_registration WHERE family_number = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $family_number);
$stmt->execute();
$result = $stmt->get_result();

$is_valid = false;
$record = null;

if ($result->num_rows > 0) {
    $is_valid = true;
    $record = $result->fetch_assoc();

    // Store validated family in session for dashboard display
    if (!isset($_SESSION['validated_families'])) {
        $_SESSION['validated_families'] = [];
    }

    // Add this validation to the session (keep only last 10)
    $validation_entry = [
        'family_number' => $record['family_number'],
        'male_name' => $record['male_name'],
        'female_name' => $record['female_name'],
        'address' => $record['address'],
        'validation_time' => date('Y-m-d H:i:s'),
        'id' => $record['id']
    ];

    // Remove if already exists to avoid duplicates
    $_SESSION['validated_families'] = array_filter($_SESSION['validated_families'], function($item) use ($record) {
        return $item['family_number'] !== $record['family_number'];
    });

    // Add to beginning of array
    array_unshift($_SESSION['validated_families'], $validation_entry);

    // Keep only last 10 validations
    $_SESSION['validated_families'] = array_slice($_SESSION['validated_families'], 0, 10);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Record Validation - La Huerta Health Center</title>
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            background: #f0f0f0;
            min-height: 100vh;
            display: flex;
            padding: 0;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }

        .dropdown-container.show {
            display: block;
        }

        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .validation-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 600px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4db3a8;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .validation-status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .valid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .invalid {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .family-info {
            background: #f8f9: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .info-group {
            margin-bottom: 15px;
            display: flex;
            align-items: baseline;
        }

        .info-group label {
            font-weight: 500;
            color: #666;
            width: 120px;
            flex-shrink: 0;
        }

        .info-group span {
            color: #333;
            flex-grow: 1;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            padding: 10px 20px;
            background: #4db3a8;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            gap: 8px;
            margin-top: 20px;
        }

        .back-btn:hover {
            background: #3a8f87;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }

            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link dropdown-btn">
                        <i class="fas fa-folder"></i>
                        Records
                    </a>
                    <div class="dropdown-container">
                        <a href="view_family_records.php" class="dropdown-link">
                            <i class="fas fa-table"></i>
                            View Records
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                            <i class="fas fa-exclamation-triangle"></i>
                            Duplicate Records
                        </a>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <a href="missing_family_numbers.php" class="dropdown-link">
                            <i class="fas fa-list-ol"></i>
                            Missing Family Numbers
                        </a>
                        <a href="recently_deleted.php" class="dropdown-link">
                            <i class="fas fa-trash-alt"></i>
                            Recently Deleted
                        </a>
                        <?php endif; ?>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link active">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-content">
            <div class="validation-container">
                <div class="header">
                    <h1>Family Record Validation</h1>
                    <p>La Huerta Health Center</p>
                </div>

                <div class="validation-status <?php echo $is_valid ? 'valid' : 'invalid'; ?>">
                    <?php if ($is_valid): ?>
                        <i class="fas fa-check-circle"></i> This Family Number is Valid
                    <?php else: ?>
                        <i class="fas fa-times-circle"></i> This Family Number ID Card is Invalid
                    <?php endif; ?>
                </div>

                <?php if ($is_valid && $record): ?>
                <div class="family-info">
                    <div class="info-group">
                        <label>Family Number:</label>
                        <span><?php echo htmlspecialchars($record['family_number']); ?></span>
                    </div>
                    <div class="info-group">
                        <label>Male Name:</label>
                        <span><?php echo htmlspecialchars($record['male_name']); ?></span>
                    </div>
                    <div class="info-group">
                        <label>Female Name:</label>
                        <span><?php echo htmlspecialchars($record['female_name']); ?></span>
                    </div>
                    <div class="info-group">
                        <label>Address:</label>
                        <span><?php echo htmlspecialchars($record['address']); ?></span>
                    </div>
                    <div class="info-group">
                        <label>Registration Date:</label>
                        <span><?php echo htmlspecialchars($record['registration_date']); ?></span>
                    </div>
                    <div class="info-group">
                        <label>Registered By:</label>
                        <span><?php echo htmlspecialchars($record['registered_by']); ?></span>
                    </div>
                </div>
                <?php endif; ?>

                <a href="scanner.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i> Back to Scanner
                </a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dropdown functionality for sidebar
            const dropdownBtns = document.querySelectorAll('.dropdown-btn');

            dropdownBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.classList.toggle('active');
                    const dropdownContent = this.nextElementSibling;
                    dropdownContent.classList.toggle('show');
                });
            });
        });

        // Timeout functionality
        let inactivityTimeout;

        function resetTimeout() {
            clearTimeout(inactivityTimeout);
            inactivityTimeout = setTimeout(function() {
                window.location.href = 'dashboard.php';
            }, 300000); // 5 minutes timeout
        }

        // Initialize timeout when page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetTimeout();

            // Reset timeout on user activity
            document.addEventListener('mousemove', resetTimeout);
            document.addEventListener('keypress', resetTimeout);
            document.addEventListener('click', resetTimeout);
            document.addEventListener('scroll', resetTimeout);

            // Add event listeners to form elements to reset timeout
            const formElements = document.querySelectorAll('input, select, textarea, button');
            formElements.forEach(element => {
                element.addEventListener('focus', resetTimeout);
                element.addEventListener('change', resetTimeout);
            });
        });
    </script>
</body>
</html>
<?php $conn->close(); ?>