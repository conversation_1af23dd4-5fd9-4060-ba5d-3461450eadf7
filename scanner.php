<?php
session_start();
include "db.php";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Record Scanner - La Huerta Health Center</title>
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            background: #f0f0f0;
            min-height: 100vh;
            display: flex;
            padding: 0;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }
        
        .dropdown-container.show {
            display: block;
        }
        
        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }

            .main-content {
                padding: 1rem;
            }
        }

        .scanner-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            animation: scaleIn 0.5s ease-out forwards;
            position: relative;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4db3a8;
            animation: fadeIn 0.6s ease-out forwards;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .input-group {
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeIn 0.7s ease-out 0.3s forwards;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .input-group input:focus {
            border-color: #4db3a8;
            outline: none;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: #4db3a8;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 0.7s ease-out 0.5s forwards;
        }

        .submit-btn:hover {
            background: #3a8f87;
            transform: translateY(-1px);
        }

        .submit-btn::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            display: none;
            animation: fadeIn 0.3s ease-out forwards;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            display: none;
            animation: fadeIn 0.3s ease-out forwards;
        }

        /* Animation styles */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.95);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes shimmer {
            100% {
                left: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link dropdown-btn">
                        <i class="fas fa-folder"></i>
                        Records
                    </a>
                    <div class="dropdown-container">
                        <a href="view_family_records.php" class="dropdown-link">
                            <i class="fas fa-table"></i>
                            View Records
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                            <i class="fas fa-exclamation-triangle"></i>
                            Duplicate Records
                        </a>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <a href="missing_family_numbers.php" class="dropdown-link">
                            <i class="fas fa-list-ol"></i>
                            Missing Family Numbers
                        </a>
                        <a href="recently_deleted.php" class="dropdown-link">
                            <i class="fas fa-trash-alt"></i>
                            Recently Deleted
                        </a>
                        <?php endif; ?>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link active">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-content">
            <div class="scanner-container">
                <div class="header">
                    <h1>Family Record Scanner</h1>
                    <p>La Huerta Health Center</p>
                </div>

                <form id="manualForm" action="validate_family.php" method="GET">
                    <div class="input-group">
                        <label for="data">Enter QR Code Data:</label>
                        <input type="text" id="data" name="data" placeholder="Paste or type QR code data here" required>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-search"></i> Validate Record
                    </button>
                </form>

                <div id="errorMessage" class="error-message"></div>
                <div id="successMessage" class="success-message"></div>
            </div>
        </div>
    </div>

    <script>
        // Form submission handler
        document.addEventListener('DOMContentLoaded', function() {
            // Add animations to input field on focus
            document.getElementById('data').addEventListener('focus', function() {
                this.style.transform = 'translateX(5px)';
                setTimeout(() => {
                    this.style.transform = 'translateX(0)';
                }, 300);
            });
            
            // Enhanced hover effect for submit button
            document.querySelector('.submit-btn').addEventListener('mouseover', function() {
                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 5px 15px rgba(77, 179, 168, 0.3)';
            });
            
            document.querySelector('.submit-btn').addEventListener('mouseout', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
            
            // Initialize dropdown functionality for sidebar
            const dropdownBtns = document.querySelectorAll('.dropdown-btn');
            
            dropdownBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.classList.toggle('active');
                    const dropdownContent = this.nextElementSibling;
                    dropdownContent.classList.toggle('show');
                });
            });
        
            // Existing form submission handler code
            document.getElementById('manualForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const data = document.getElementById('data').value.trim();
                
                if (!data) {
                    document.getElementById('errorMessage').textContent = 'Please enter QR code data';
                    document.getElementById('errorMessage').style.display = 'block';
                    return;
                }

                try {
                    // Check if the input is a URL
                    if (data.startsWith('http')) {
                        const url = new URL(data);
                        const urlData = url.searchParams.get('data');
                        if (urlData) {
                            // Decode the base64 data
                            const decodedData = atob(urlData);
                            window.location.href = 'validate_family.php?data=' + encodeURIComponent(decodedData);
                        } else {
                            document.getElementById('errorMessage').textContent = 'Invalid QR code data. Please enter a valid family ID card URL.';
                            document.getElementById('errorMessage').style.display = 'block';
                        }
                    } else {
                        // If it's just the data parameter, use it directly
                        window.location.href = 'validate_family.php?data=' + encodeURIComponent(data);
                    }
                } catch (error) {
                    document.getElementById('errorMessage').textContent = 'Invalid QR code format. Please enter a valid family ID card URL or data.';
                    document.getElementById('errorMessage').style.display = 'block';
                }
            });
        });

        // Timeout functionality
        let inactivityTimeout;
        
        function resetTimeout() {
            clearTimeout(inactivityTimeout);
            inactivityTimeout = setTimeout(function() {
                window.location.href = 'dashboard.php';
            }, 300000); // 5 minutes timeout
        }
        
        // Initialize timeout when page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetTimeout();
            
            // Reset timeout on user activity
            document.addEventListener('mousemove', resetTimeout);
            document.addEventListener('keypress', resetTimeout);
            document.addEventListener('click', resetTimeout);
            document.addEventListener('scroll', resetTimeout);
            
            // Add event listeners to form elements to reset timeout
            const formElements = document.querySelectorAll('input, select, textarea, button');
            formElements.forEach(element => {
                element.addEventListener('focus', resetTimeout);
                element.addEventListener('change', resetTimeout);
            });
            
            // If scanner has any specific interactions, add timeout reset to those
            const scannerElements = document.querySelectorAll('.scanner-related-elements');
            scannerElements.forEach(element => {
                element.addEventListener('click', resetTimeout);
            });
        });
    </script>
</body>
</html> 