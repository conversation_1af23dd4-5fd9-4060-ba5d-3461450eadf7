<?php
session_start();
if (!isset($_SESSION['username'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Check if the request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid request method']);
    exit();
}

// Get the data from the POST request
$type = isset($_POST['type']) ? $_POST['type'] : '';
$name = isset($_POST['name']) ? trim($_POST['name']) : '';

// Validate input
if (empty($type) || empty($name) || ($type !== 'male' && $type !== 'female')) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid parameters']);
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// Prepare the query based on the type of name being checked
$field = ($type === 'male') ? 'male_name' : 'female_name';
$sql = "SELECT id, family_number FROM family_registration WHERE UPPER(TRIM($field)) = ?";

$stmt = $conn->prepare($sql);
$uppercaseName = strtoupper($name);
$stmt->bind_param("s", $uppercaseName);
$stmt->execute();
$result = $stmt->get_result();

// Process the results
$count = $result->num_rows;
$familyNumbers = [];

if ($count > 0) {
    while ($row = $result->fetch_assoc()) {
        $familyNumbers[] = $row['family_number'];
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'exists' => true,
        'count' => $count,
        'familyNumbers' => $familyNumbers
    ]);
} else {
    header('Content-Type: application/json');
    echo json_encode(['exists' => false]);
}

$stmt->close();
$conn->close();
?> 