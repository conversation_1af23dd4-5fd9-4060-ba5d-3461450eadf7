# System Patterns - Family Registration System

## Architectural Patterns

### 1. Monolithic Architecture
The Family Registration System uses a monolithic architecture where all components are interconnected and deployed as a single unit. This approach was chosen for simplicity and rapid development, suitable for the scale of the application.

### 2. Model-View-Controller (MVC) Inspired Structure
While not strictly following MVC, the system implements a logical separation of concerns:
- **Data Layer**: Database interactions and data processing
- **Presentation Layer**: UI components and rendering
- **Logic Layer**: Business rules and application flow

### 3. Shared Session Management
User authentication and session management are handled through PHP sessions, providing a consistent authentication mechanism across all pages.

## Code Organization

### File Structure
```
Family Registration System
│
├── Core System Files
│   ├── login.php - User authentication
│   ├── logout.php - Session termination
│   ├── dashboard.php - Main dashboard with statistics
│   └── db.php - Database connection
│
├── Family Management
│   ├── family_registration.php - Add new families
│   ├── view_family_records.php - View and filter family records
│   ├── edit_family.php - Edit existing family records
│   ├── delete_family.php - Remove family records
│   ├── print_family.php - Print family details
│   ├── scanner.php - Scan family QR codes
│   ├── missing_family_numbers.php - Track gaps in numbering sequence
│   ├── recently_deleted.php - Manage deleted records
│   └── validate_family.php - Validate family records
│
├── User Management
│   ├── manage_users.php - Add, edit, remove users
│   ├── update_role.php - Update user roles
│   ├── check_user_records.php - Check user activity
│   └── get_user_password.php - Password management
│
├── Data Operations
│   ├── search_family.php - Search functionality
│   ├── check_name_exists.php - Check for duplicate names
│   ├── export_excel.php - Export data to Excel
│   ├── import_excel.php - Import data from Excel
│   └── undo_last_import.php - Revert import operations
│
├── API Endpoints
│   ├── get_registration_stats.php - Data for statistics charts
│   ├── get_pie_chart_data.php - Pie chart data
│   └── get_next_family_number.php - Retrieve next available number
│
├── Static Assets
│   └── images/ - Logos and graphics
│
├── Database Setup
│   └── create_database.sql - Database creation script
│
└── Documentation
    ├── project_brief.md - Project overview
    ├── active_context.md - Current state and focus
    ├── product_context.md - Business and user context
    ├── progress.md - Development progress
    ├── system_patterns.md - Design patterns and architecture
    └── index.md - Documentation navigation
```

### Naming Conventions
- **Files**: Descriptive lowercase names with underscores for separation
- **Variables**: Camel case for variables (e.g., `$familyNumber`)
- **Database Tables**: Lowercase with underscores (e.g., `family_registration`)
- **Constants**: Uppercase with underscores (e.g., `MAX_RESULTS`)

## Design Patterns

### 1. Front Controller Pattern
The system uses a semi-front controller approach, where common header/navigation elements are consistent across pages, and access control is implemented at the beginning of each script.

### 2. Data Access Object (DAO) Pattern
Database operations are encapsulated in prepared statements, providing a consistent approach to data access and manipulation.

### 3. Factory-like Pattern for Forms
Form generation follows a consistent pattern, with reusable components for input fields, validation, and error handling.

### 4. Observer-like Pattern for Notifications
Event-driven notifications are implemented for operations like record deletions and restorations, where the system responds to observed data changes.

## Coding Standards

### PHP Standards
- PHP 7+ compatible code
- Error reporting and handling
- Input sanitization for security
- Prepared statements for database queries
- Session management best practices

### JavaScript Standards
- Event delegation for dynamic elements
- Fetch API for asynchronous requests
- Clean separation of functionality
- Error handling for API calls
- Consistent code formatting

### HTML/CSS Standards
- Semantic HTML5 elements
- Mobile-first responsive design
- CSS class naming conventions
- Consistent color schemes and typography
- Accessible UI components

## Data Flow Patterns

### Authentication Flow
1. User submits login credentials
2. Server validates credentials against database
3. Upon success, session is established with user role
4. Access controls applied based on session data
5. Session timeout handling for security

### Form Submission Flow
1. Client-side validation of user input
2. AJAX requests for real-time validation (e.g., next family number)
3. Server-side validation on submission
4. Database operation (insert/update)
5. Response/feedback to user
6. Redirect or state update

### Data Retrieval Flow
1. User initiates search or filter request
2. Parameters validated and sanitized
3. Database query with prepared statements
4. Result pagination and formatting
5. Display and interaction options provided

## Common Implementation Patterns

### UI Components
- Action cards for dashboard navigation
- Form groups with consistent styling
- Data tables with sorting and filtering
- Modal dialogs for confirmations and detailed views
- Toast notifications for user feedback
- Responsive sidebar navigation
- Date/time display in header

### Security Controls
- Session validation at the start of each PHP file
- Role-based access control for sensitive operations
- SQL injection prevention through prepared statements
- Input sanitization for all user-provided data
- CSRF protection for form submissions

### Error Handling
- Graceful degradation for JavaScript failures
- User-friendly error messages
- Server-side logging of errors
- Validation feedback at point of entry
- Consistent error display patterns

## Integration Patterns

### Chart.js Integration
- Data fetched through dedicated API endpoints
- Consistent configuration objects
- Responsive sizing and styling
- Centralized color schemes
- Reusable option configurations

### Import/Export Integration
- Standardized Excel format definition
- Validation of imported data
- Transaction-based import processing
- Error handling for malformed data
- Summary reporting of results

## Future Pattern Considerations

### Modularization
Future versions could benefit from more modular code organization, potentially implementing:
- Shared utility functions in dedicated files
- Class-based components for complex functionality
- Configuration separation from implementation
- Template/view separation

### API Architecture
As the system grows, an API-first approach might be beneficial:
- RESTful endpoints for all operations
- Front-end/back-end separation
- Standardized response formats
- Authentication tokens instead of sessions
- Versioned API endpoints 