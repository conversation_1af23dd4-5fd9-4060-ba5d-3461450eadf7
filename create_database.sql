-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS lhhc;

-- Use the database
USE lhhc;

-- Create the users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'staff') NOT NULL DEFAULT 'staff',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create the family registration table
CREATE TABLE IF NOT EXISTS family_registration (
    id INT AUTO_INCREMENT PRIMARY KEY,
    registration_date DATE NOT NULL,
    family_number VA<PERSON>HAR(50) NOT NULL,
    male_name VARCHA<PERSON>(100) NOT NULL,
    female_name VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    remarks TEXT,
    registered_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (registered_by) REFERENCES users(username)
); 