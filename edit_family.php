<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get record ID from URL
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Fetch existing record first
$sql = "SELECT * FROM family_registration WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();
$family = $result->fetch_assoc();

if (!$family) {
    header("Location: view_family_records.php");
    exit();
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Convert the date format from "MMM DD, YYYY" to "YYYY-MM-DD" for database storage
    $registration_date = date('Y-m-d', strtotime($_POST['registration_date']));
    
    // Handle family number based on user role
    if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin') {
        $family_number = strtoupper($_POST['family_number']);
    } else {
        // For non-admin users, keep the original family number
        $family_number = $family['family_number'];
    }
    
    $male_name = strtoupper($_POST['male_name']);
    $female_name = strtoupper($_POST['female_name']);
    $address = strtoupper($_POST['address']);
    $remarks = strtoupper($_POST['remarks']);

    $sql = "UPDATE family_registration SET 
            registration_date = ?,
            family_number = ?,
            male_name = ?,
            female_name = ?,
            address = ?,
            remarks = ?
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssssi", $registration_date, $family_number, $male_name, $female_name, $address, $remarks, $id);
    
    if ($stmt->execute()) {
        echo "<script>
            alert('Family record updated successfully!');
            window.location.href = 'view_family_records.php';
        </script>";
    } else {
        echo "<script>
            alert('Error updating record: " . $conn->error . "');
            window.location.href = 'view_family_records.php';
        </script>";
    }
    
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Family Record</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            min-height: 100vh;
            background: #f0f7ff;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }
        
        .dropdown-container.show {
            display: block;
        }
        
        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            display: flex;
            justify-content: center;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            padding: 3rem;
            margin-top: 1rem;
            position: relative;
            overflow: hidden;
            animation: scaleIn 0.5s ease-out forwards;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
        }

        .header {
            text-align: center;
            margin-bottom: 2.5rem;
            animation: fadeIn 0.6s ease-out forwards;
        }

        .header h2 {
            color: #000;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 1.8rem;
            opacity: 0;
            animation: fadeIn 0.5s ease-out forwards;
        }

        .form-group:nth-child(1) { animation-delay: 0.2s; }
        .form-group:nth-child(2) { animation-delay: 0.3s; }
        .form-group:nth-child(3) { animation-delay: 0.4s; }
        .form-group:nth-child(4) { animation-delay: 0.5s; }
        .form-group:nth-child(5) { animation-delay: 0.6s; }
        .form-group:nth-child(6) { animation-delay: 0.7s; }

        .form-group label {
            display: block;
            margin-bottom: 0.8rem;
            color: #000;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #555;
            font-size: 1.1rem;
            z-index: 2;
            pointer-events: none;
            opacity: 0.8;
        }

        /* Calendar icon specific */
        .input-group .fa-calendar,
        .input-group .fa-hashtag {
            color: #4a5568;
        }
        
        /* Gender icons specific */
        .input-group .fa-mars {
            color: #3182ce;
        }
        
        .input-group .fa-venus {
            color: #d53f8c;
        }
        
        /* Map and comment icons */
        .input-group .fa-map-marker-alt {
            color: #718096;
        }
        
        .input-group .fa-comment {
            color: #718096;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
            color: #333;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        /* Family number field styling */
        #family_number {
            font-weight: 600;
            letter-spacing: 0.5px;
            color: #333;
            font-size: 1.1rem;
            transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
            padding-right: 3rem;
        }
        
        /* Animation for the family number value */
        .highlight-value {
            background-color: #e9ecef !important;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            transform: scale(1.02);
            letter-spacing: 1px;
        }
        
        /* Textarea special styling for address and remarks */
        .form-group textarea {
            padding: 1rem 1rem 1rem 3rem;
            height: 120px;
            resize: vertical;
        }
        
        /* Ensure the map-marker and comment icons stay at the top */
        .form-group:nth-child(5) .input-group i,
        .form-group:nth-child(6) .input-group i {
            top: 1.5rem;
            transform: none;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4db3a8;
            background: white;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        /* Animation styles */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.9);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes shimmer {
            100% {
                left: 100%;
            }
        }

        .message {
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            color: #000;
            animation: scaleIn 0.4s ease-out forwards;
        }

        .success {
            background: #f0fff4;
            border: 1px solid #c8f7d4;
        }

        .error {
            background: #fff5f5;
            border: 1px solid #fde8e8;
            border-left: 4px solid #e53e3e;
        }

        .error i {
            color: #e53e3e;
        }

        .message i {
            font-size: 1.2rem;
        }

        .btn-container {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            opacity: 0;
            animation: fadeIn 0.5s ease-out 0.8s forwards;
        }

        .submit-btn, .cancel-btn {
            flex: 1;
            padding: 1rem;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .submit-btn {
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            border: none;
        }

        .cancel-btn {
            background: #f0f0f0;
            color: #000;
            border: none;
        }

        .submit-btn:hover, .cancel-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .submit-btn:active, .cancel-btn:active {
            transform: translateY(0);
        }

        .submit-btn::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        footer {
            padding: 10px;
            text-align: center;
            border-top: 1px solid #ddd;
            font-size: 13px;
            color: #666;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }

            .main-content {
                padding: 1rem;
            }

            .container {
                padding: 2rem;
            }

            .btn-container {
                flex-direction: column;
            }
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize flatpickr for date picker
            flatpickr("#registration_date", {
                dateFormat: "M d, Y",
                allowInput: true,
                altInput: true,
                altFormat: "M d, Y",
                defaultDate: "<?php echo date('M d, Y', strtotime($family['registration_date'])); ?>"
            });

            // Form submission validation
            $('form').on('submit', function(e) {
                // Add any validation if needed
            });
            
            // Animate the family number field on load
            setTimeout(function() {
                $('#family_number').addClass('highlight-value');
                setTimeout(function() {
                    $('#family_number').removeClass('highlight-value');
                }, 1500);
            }, 1200);
            
            // Add hover effects to input fields
            $('.form-group input, .form-group textarea').hover(
                function() {
                    $(this).css('transform', 'translateY(-2px)');
                    $(this).css('box-shadow', '0 4px 12px rgba(0,0,0,0.1)');
                },
                function() {
                    $(this).css('transform', 'translateY(0)');
                    $(this).css('box-shadow', '0 1px 2px rgba(0,0,0,0.05)');
                }
            );
            
            // Add focus animations
            $('.form-group input, .form-group textarea').on('focus', function() {
                $(this).parent().find('i').css('transform', 'translateY(-50%) scale(1.2)');
                $(this).parent().find('i').css('opacity', '1');
            }).on('blur', function() {
                $(this).parent().find('i').css('transform', 'translateY(-50%)');
                $(this).parent().find('i').css('opacity', '0.8');
            });
            
            // Special handling for textarea icons
            $('.form-group:nth-child(5) input, .form-group:nth-child(5) textarea, .form-group:nth-child(6) input, .form-group:nth-child(6) textarea').on('focus', function() {
                $(this).parent().find('i').css('transform', 'scale(1.2)');
            }).on('blur', function() {
                $(this).parent().find('i').css('transform', 'none');
            });
        });
    </script>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link dropdown-btn active">
                        <i class="fas fa-folder"></i>
                        Records
                    </a>
                    <div class="dropdown-container show">
                        <a href="view_family_records.php" class="dropdown-link active">
                            <i class="fas fa-table"></i>
                            View Records
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                            <i class="fas fa-exclamation-triangle"></i>
                            Duplicate Records
                        </a>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <a href="missing_family_numbers.php" class="dropdown-link">
                            <i class="fas fa-list-ol"></i>
                            Missing Family Numbers
                        </a>
                        <a href="recently_deleted.php" class="dropdown-link">
                            <i class="fas fa-trash-alt"></i>
                            Recently Deleted
                        </a>
                        <?php endif; ?>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="container">
                <div class="header">
                    <h2>Edit Family Record</h2>
                </div>
                
                <?php if (isset($success_message)): ?>
                    <div class="message success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="message error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . "?id=" . $id); ?>">
                    <div class="form-group">
                        <label for="registration_date">Registration Date</label>
                        <div class="input-group">
                            <i class="fas fa-calendar"></i>
                            <input type="text" id="registration_date" name="registration_date" required 
                                   value="<?php echo date('M d, Y', strtotime($family['registration_date'])); ?>"
                                   placeholder="MMM DD, YYYY">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="family_number">Family Number</label>
                        <div class="input-group">
                            <i class="fas fa-hashtag"></i>
                            <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                                <input type="text" id="family_number" name="family_number" required 
                                       value="<?php echo htmlspecialchars($family['family_number']); ?>">
                            <?php else: ?>
                                <input type="text" id="family_number" name="family_number" required 
                                       value="<?php echo htmlspecialchars($family['family_number']); ?>"
                                       readonly style="background: #f0f0f0; cursor: not-allowed;">
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="male_name">Male Name</label>
                        <div class="input-group">
                            <i class="fas fa-mars"></i>
                            <input type="text" id="male_name" name="male_name" 
                                   value="<?php echo htmlspecialchars($family['male_name']); ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="female_name">Female Name</label>
                        <div class="input-group">
                            <i class="fas fa-venus"></i>
                            <input type="text" id="female_name" name="female_name" 
                                   value="<?php echo htmlspecialchars($family['female_name']); ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address">Address</label>
                        <div class="input-group">
                            <i class="fas fa-map-marker-alt"></i>
                            <textarea id="address" name="address" required><?php echo htmlspecialchars($family['address']); ?></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="remarks">Remarks</label>
                        <div class="input-group">
                            <i class="fas fa-comment"></i>
                            <textarea id="remarks" name="remarks"><?php echo htmlspecialchars($family['remarks']); ?></textarea>
                        </div>
                    </div>

                    <div class="btn-container">
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-save"></i>
                            Update Record
                        </button>
                        <a href="view_family_records.php" class="cancel-btn">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </form>
                
                <footer>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>&copy; <?php echo date("Y"); ?> Family Number Management System</span>
                        <span style="margin-right: 10px;">HIMU-Justin</span>
                    </div>
                </footer>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dropdown functionality for sidebar
            const dropdownBtns = document.querySelectorAll('.dropdown-btn');
            
            dropdownBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.classList.toggle('active');
                    const dropdownContent = this.nextElementSibling;
                    dropdownContent.classList.toggle('show');
                });
            });
        });
    </script>
</body>
</html>
<?php $conn->close(); ?> 