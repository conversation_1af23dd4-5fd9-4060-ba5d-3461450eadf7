<?php
session_start();
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

if (!isset($_SESSION['username']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'super admin')) {
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
    exit();
}

if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid family ID']);
    exit();
}

// Log request details for debugging
error_log("Delete request received for ID: " . $_POST['id']);

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    error_log("Database connection failed: " . $conn->connect_error);
    echo json_encode(['status' => 'error', 'message' => 'Database connection failed: ' . $conn->connect_error]);
    exit();
}

// Start transaction
$conn->begin_transaction();

try {
    $id = (int)$_POST['id'];
    
    // Get the record to archive before deletion
    $select_stmt = $conn->prepare("SELECT * FROM family_registration WHERE id = ?");
    if (!$select_stmt) {
        throw new Exception("Prepare failed for select: " . $conn->error);
    }
    
    $select_stmt->bind_param("i", $id);
    if (!$select_stmt->execute()) {
        throw new Exception("Execute failed for select: " . $select_stmt->error);
    }
    
    $result = $select_stmt->get_result();
    
    if ($record = $result->fetch_assoc()) {
        // Log the record we found
        error_log("Found record to delete: Family #" . $record['family_number']);
        
        // Check if deleted_records table exists, if not create it
        $table_check = $conn->query("SHOW TABLES LIKE 'deleted_records'");
        if ($table_check->num_rows === 0) {
            $create_table_sql = "CREATE TABLE deleted_records (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                original_id INT(11),
                registration_date DATE,
                family_number VARCHAR(20),
                male_name VARCHAR(100),
                female_name VARCHAR(100),
                address TEXT,
                remarks TEXT,
                registered_by VARCHAR(50),
                deleted_by VARCHAR(50),
                deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                deletion_reason VARCHAR(255) NULL
            )";
            
            if (!$conn->query($create_table_sql)) {
                throw new Exception("Failed to create deleted_records table: " . $conn->error);
            }
            
            // Add index for better performance
            $conn->query("ALTER TABLE deleted_records ADD INDEX (family_number)");
            $conn->query("ALTER TABLE deleted_records ADD INDEX (deleted_at)");
            $conn->query("ALTER TABLE deleted_records ADD INDEX (registration_date)");
        } else {
            // Check if deletion_reason column exists
            $column_check = $conn->query("SHOW COLUMNS FROM deleted_records LIKE 'deletion_reason'");
            if ($column_check->num_rows === 0) {
                // Add the column if it doesn't exist
                $add_column_sql = "ALTER TABLE deleted_records ADD COLUMN deletion_reason VARCHAR(255) NULL";
                if (!$conn->query($add_column_sql)) {
                    throw new Exception("Failed to add deletion_reason column: " . $conn->error);
                }
                error_log("Added deletion_reason column to deleted_records table");
            }
        }
        
        // Check if the deletion_reason column exists
        $column_exists = $conn->query("SHOW COLUMNS FROM deleted_records LIKE 'deletion_reason'")->num_rows > 0;

        // Get the deletion reason from POST data
        $deletion_reason = isset($_POST['reason']) ? trim($_POST['reason']) : null;
        error_log("Deletion reason: " . ($deletion_reason ? $deletion_reason : 'Not provided'));

        // Dynamically construct the insert SQL based on column existence
        if ($column_exists) {
            // Complete insert with all necessary fields including deletion_reason
            $archive_sql = "INSERT INTO deleted_records 
                (original_id, registration_date, family_number, male_name, female_name, address, remarks, registered_by, deleted_by, deletion_reason) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
            $archive_stmt = $conn->prepare($archive_sql);
            if (!$archive_stmt) {
                throw new Exception("Prepare failed for archive: " . $conn->error);
            }
            
            // Handle potential NULL values for remarks
            $remarks = isset($record['remarks']) ? $record['remarks'] : null;
            
            $archive_stmt->bind_param(
                "isssssssss", 
                $record['id'],
                $record['registration_date'],
                $record['family_number'],
                $record['male_name'],
                $record['female_name'],
                $record['address'],
                $remarks,
                $record['registered_by'],
                $_SESSION['username'],
                $deletion_reason
            );
        } else {
            // Insert without the deletion_reason column
            $archive_sql = "INSERT INTO deleted_records 
                (original_id, registration_date, family_number, male_name, female_name, address, remarks, registered_by, deleted_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
            $archive_stmt = $conn->prepare($archive_sql);
            if (!$archive_stmt) {
                throw new Exception("Prepare failed for archive: " . $conn->error);
            }
            
            // Handle potential NULL values for remarks
            $remarks = isset($record['remarks']) ? $record['remarks'] : null;
            
            $archive_stmt->bind_param(
                "issssssss", 
                $record['id'],
                $record['registration_date'],
                $record['family_number'],
                $record['male_name'],
                $record['female_name'],
                $record['address'],
                $remarks,
                $record['registered_by'],
                $_SESSION['username']
            );
        }
        
        if (!$archive_stmt->execute()) {
            throw new Exception("Archive failed: " . $archive_stmt->error);
        }
        
        error_log("Record archived successfully");
        
        // Delete the record (this is the critical part)
        $delete_sql = "DELETE FROM family_registration WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        if (!$delete_stmt) {
            throw new Exception("Prepare failed for delete: " . $conn->error);
        }
        
        $delete_stmt->bind_param("i", $id);
        if (!$delete_stmt->execute()) {
            throw new Exception("Delete failed: " . $delete_stmt->error);
        }
        
        // If we get here, the delete worked
        error_log("Record deleted successfully");
        
        // Commit transaction
        $conn->commit();
        
        echo json_encode([
            'status' => 'success', 
            'message' => 'Family record deleted successfully'
        ]);
    } else {
        throw new Exception("Record not found");
    }
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    // Log detailed error
    error_log("Delete family record error: " . $e->getMessage());
    
    // Return error to client
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}

$conn->close();
?> 