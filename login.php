<?php 
session_start(); 
if (!isset($_SESSION)) {
    session_start();
}

include "db.php"; 

// Function to log errors
function logError($message) {
    error_log($message);
}

// Handle login
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    // First check if failed_attempts column exists
    $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'failed_attempts'");
    if ($check_column->num_rows == 0) {
        // Add the column if it doesn't exist
        $conn->query("ALTER TABLE users ADD COLUMN failed_attempts INT DEFAULT 0 AFTER status");
    }
    
    // Debug: Print received credentials (remove in production)
    error_log("Login attempt - Username: " . $username);
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT id, username, password, role, status, failed_attempts FROM users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        // Check if account is already deactivated
        if ($user['status'] == 'inactive') {
            $error = "Your account has been deactivated due to too many failed login attempts. Please contact the administrator.";
        } else {
            // Verify password
            if (password_verify($password, $user['password'])) {
                // Reset failed attempts on successful login
                $reset_stmt = $conn->prepare("UPDATE users SET failed_attempts = 0 WHERE id = ?");
                $reset_stmt->bind_param("i", $user['id']);
                $reset_stmt->execute();
                
                // Check if last_login column exists
                $check_last_login = $conn->query("SHOW COLUMNS FROM users LIKE 'last_login'");
                if ($check_last_login->num_rows > 0) {
                    // Update last login time
                    $update_login_time = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                    $update_login_time->bind_param("i", $user['id']);
                    $update_login_time->execute();
                } else {
                    // Add last_login column if it doesn't exist
                    $conn->query("ALTER TABLE users ADD COLUMN last_login DATETIME NULL AFTER failed_attempts");
                    // Then update it
                    $update_login_time = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                    $update_login_time->bind_param("i", $user['id']);
                    $update_login_time->execute();
                }
                
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['logged_in'] = true;
                
                // Only set these if they exist in the database
                if (isset($user['fullname'])) {
                    $_SESSION['fullname'] = $user['fullname'];
                }
                if (isset($user['designation'])) {
                    $_SESSION['designation'] = $user['designation'];
                }
                
                // Log successful login
                $log_message = "User " . $user['username'] . " logged in successfully";
                logError($log_message);
                
                // Redirect based on role
                if ($user['role'] == 'admin' || $user['role'] == 'super admin' || $user['role'] == 'staff') {
                    header("Location: dashboard.php");
                } else {
                    header("Location: validate_family.php");
                }
                exit();
            } else {
                // Increment failed attempts
                $new_attempts = $user['failed_attempts'] + 1;
                $update_stmt = $conn->prepare("UPDATE users SET failed_attempts = ? WHERE id = ?");
                $update_stmt->bind_param("ii", $new_attempts, $user['id']);
                $update_stmt->execute();
                
                // Check if account should be deactivated
                if ($new_attempts >= 5) {
                    $deactivate_stmt = $conn->prepare("UPDATE users SET status = 'inactive' WHERE id = ?");
                    $deactivate_stmt->bind_param("i", $user['id']);
                    $deactivate_stmt->execute();
                    
                    $error = "Your account has been deactivated due to too many failed login attempts. Please contact the administrator.";
                    
                    // Log account deactivation
                    $log_message = "User " . $user['username'] . "'s account was deactivated due to 5 failed login attempts";
                    logError($log_message);
                } else {
                    $attempts_remaining = 5 - $new_attempts;
                    $error = "Invalid username or password. " . $attempts_remaining . " attempts remaining before account deactivation.";
                    
                    // Log failed attempt
                    $log_message = "Failed login attempt for user " . $user['username'] . " (Attempt " . $new_attempts . " of 5)";
                    logError($log_message);
                }
            }
        }
    } else {
        $error = "Invalid username or password";
        // Log failed attempt with non-existent username
        $log_message = "Failed login attempt with non-existent username: " . $username;
        logError($log_message);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f7ff;
            position: relative;
            margin: 0;
            padding: 0;
        }

        body::before {
            content: '';
            position: fixed;
            top: -100px;
            left: 0;
            width: 100%;
            height: calc(100% + 100px);
            background: url('images/pque cityhall.png') no-repeat center center;
            background-size: cover;
            z-index: -2;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: -1;
        }

        .login-container {
            opacity:0.9;
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 420px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(5px);
            margin: 1rem;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-header h2 {
            color: #4db3a8;
            font-size: 1.4rem;
            margin-bottom: 0.3rem;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 1.8rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.1rem;
            color: #4db3a8;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .input-group {
            position: relative;
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .input-group:focus-within {
            border-color: #4db3a8;
            background: white;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        .input-group i {
            position: absolute;
            left: 1rem;
            color: #95a5a6;
            font-size: 1.1rem;
            z-index: 1;
        }

        .form-group input {
            width: 100%;
            padding: 1rem 3rem 1rem 3rem;
            border: none;
            font-size: 1rem;
            background: transparent;
            color: #333;
        }

        .form-group input:focus {
            outline: none;
        }

        .toggle-password {
            position: absolute;
            right: 1rem;
            background: none;
            border: none;
            cursor: pointer;
            color: #95a5a6;
            padding: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toggle-password:hover {
            color: #4db3a8;
        }

        .toggle-password:focus {
            outline: none;
        }

        .toggle-password i {
            font-size: 1.1rem;
        }

        .submit-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .error-message {
            background: #fff5f5;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .inactive-notice {
            background: #fff3cd;
            color: #856404;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            border: 1px solid #ffeeba;
        }

        .inactive-notice i {
            color: #856404;
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2>LA HUERTA HEALTH CENTER</h2>
            <h2>Login</h2>
            <p>Please enter your credentials</p>
        </div>

        <?php if (isset($error)): ?>
            <?php if (strpos($error, "inactive") !== false): ?>
                <div class="inactive-notice">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo $error; ?>
                </div>
            <?php else: ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
            <div class="form-group">
                <label for="username">Username</label>
                <div class="input-group">
                    <i class="fas fa-user"></i>
                    <input type="text" id="username" name="username" required placeholder="Enter your username">
                </div>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="password" name="password" required placeholder="Enter your password">
                    <button type="button" class="toggle-password" onclick="togglePassword()">
                        <i class="fas fa-eye" style="position: static; transform: none;"></i>
                    </button>
                </div>
            </div>

            <button type="submit" class="submit-btn">
                <i class="fas fa-sign-in-alt"></i>
                Login
            </button>
            <footer style="margin-top: 20px; padding: 10px; text-align: center; border-top: 1px solid #ddd; font-size: 10.5px; color: #666;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <span>&copy; <?php echo date("Y"); ?> CITY HEALTH OFFICE / HIMU - All Rights Reserved.</span>
    </div>
</footer>
        </form>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.classList.remove('fa-eye');
                toggleBtn.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleBtn.classList.remove('fa-eye-slash');
                toggleBtn.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
<?php $conn->close(); ?>
