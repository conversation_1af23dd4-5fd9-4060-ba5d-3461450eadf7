# Family Registration System for Barangay La Huerta

A comprehensive web-based application designed for Barangay La Huerta's Health Information Management Unit (HIMU) to efficiently manage family registrations, track health records, and provide administrative functions.

## Key Features

- Family registration with automated sequential numbering system
- Comprehensive record management with advanced search capabilities
- Missing family number detection and tracking
- Recently deleted records management with restore functionality
- Role-based access control (Super Admin and Admin roles)
- Data visualization with statistics dashboard
- QR code scanning functionality for quick record access
- Import/Export functionality for Excel files
- Mobile-responsive design for use on various devices

## Technology Stack

- Frontend: HTML5, CSS3, JavaScript
- Data Visualization: Chart.js
- Icons: Font Awesome
- Backend: PHP (Native)
- Database: MySQL
- UI Framework: Custom CSS with responsive design

## Setup Instructions

1. Deploy files to your web server's document root directory
2. Create a MySQL database named 'lhhc'
3. Import the database schema from `create_database.sql`
4. Configure database connection in `db.php`
5. Access the application through your web browser
6. Default login credentials can be created through the initial setup process

## System Requirements

- Web server (Apache/XAMPP recommended)
- PHP 7.0+
- MySQL 5.7+
- Modern web browser with JavaScript enabled

## Documentation

System documentation is available in the [docs folder](docs/). This includes:
- [Project Brief](docs/project_brief.md)
- [System Patterns](docs/system_patterns.md)
- [Progress Documentation](docs/progress.md)

## License

© 2025 Barangay La Huerta Health Information Management Unit. All rights reserved. 