<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Database connection
include "db.php";

if (!isset($_GET['id'])) {
    die("No record ID specified");
}

$id = (int)$_GET['id'];
$sql = "SELECT * FROM family_registration WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    die("Record not found");
}

$record = $result->fetch_assoc();

// Generate validation number (combination of date and ID)
$validation_number = date('Ymd', strtotime($record['registration_date'])) . str_pad($id, 6, '0', STR_PAD_LEFT);

// Create QR code data - simplified format
$qr_data = $record['family_number'];  // Just use the family number directly

// Create validation URL with base64 encoded data
$encoded_data = base64_encode($qr_data);
$validation_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/validate_family.php?data=" . urlencode($encoded_data);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family ID Card - <?php echo htmlspecialchars($record['family_number']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .id-card {
            width: 4.25in;
            height: 5.5in;
            background: white;
            border: 1px solid #666;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
            margin: auto;
            display: flex;
            flex-direction: column;
        }

        .card-header {
            background: #4db3a8;
            padding: 12px;
            border-radius: 10px 10px 0 0;
            text-align: center;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .logo {
            width: 45px;
            height: 45px;
            object-fit: contain;
        }

        .card-header h1 {
            color: white;
            font-size: 16px;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .card-header h2 {
            color: white;
            font-size: 14px;
            margin-top: 3px;
            font-weight: 400;
        }

        .card-body {
            padding: 12px 15px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .info-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 12px;
        }

        .info-group {
            margin-bottom: 0;
            font-size: 13px;
            display: flex;
            align-items: baseline;
        }

        .info-group label {
            font-weight: 500;
            color: #666;
            width: 110px;
            flex-shrink: 0;
        }

        .info-group span {
            color: #333;
            flex-grow: 1;
            font-weight: 500;
        }
             
        .family-number 
            font-weight: 700;
            font-size: 16px;
            color: #000;
         
        .qr-section {
            text-align: center;
            margin-top: auto;
            padding: 8px;
            border-top: -5px solid #eee;
        }

        #qrcode {
            margin: 0 auto;
            width: 191px;
            height: 191px;
            padding: 0;
            background: white;
            border-radius: 0;
            box-shadow: none;
        }

        #qrcode img {
            width: 100% !important;
            height: 100% !important;
        }

        .validation-info {
            margin-top: 8px;
            font-size: 10px;
            color: #666;
            text-align: center;
            padding: 0;
            background: none;
            border-radius: 0;
        }

        .validation-info p {
            margin: 1px 0;
        }

        .validation-number {
            font-weight: bold;
            color: #333;
            font-size: 11px;
            margin: 3px 0;
        }

        .card-footer {
            position: absolute;
            bottom: 10px;
            left: 15px;
            right: 15px;
            text-align: center;
            padding-top: 8px;
            border-top: 1px dashed #ccc;
        }

        .note {
            font-size: 10px;
            color: #333;
            font-style: italic;
            line-height: 1.3;
        }

        .print-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #4db3a8;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .print-btn:hover {
            background: #3a8f87;
        }

        @media print {
            body {
                background: none;
                padding: 0;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .id-card {
                box-shadow: none;
                border: 1px solid #666 !important;
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .card-header {
                background: #4db3a8 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .card-header h1, .card-header h2 {
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .info-group label {
                color: #666 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .info-group span {
                color: #333 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .family-number {
                color: #000 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .validation-info {
                color: #666 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .validation-number {
                color: #333 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .note {
                color: #333 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .print-btn {
                display: none;
            }

            @page {
                size: 4.25in 5.5in portrait;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="id-card">
        <div class="card-header">
            <div class="logo-container">
                <img src="./images/brgy_la_huerta.png" alt="Barangay Logo" class="logo">
                <img src="./images/himu-logo.png" alt="HIMU Logo" class="logo">
            </div>
            <h1>Barangay La Huerta</h1>
            <h2>Family Number Card</h2>
        </div>

        <div class="card-body">
            <div class="info-section">
                <div class="info-group">
                    <label>Family No:</label>
                    <span class="family-number"><?php echo htmlspecialchars($record['family_number']); ?></span>
                </div>
                <div class="info-group">
                    <label>Male Name:</label>
                    <span><?php echo htmlspecialchars($record['male_name']); ?></span>
                </div>
                <div class="info-group">
                    <label>Female Name:</label>
                    <span><?php echo htmlspecialchars($record['female_name']); ?></span>
                </div>
                <div class="info-group">
                    <label>Address:</label>
                    <span><?php echo htmlspecialchars($record['address']); ?></span>
                </div>
                <div class="info-group">
                    <label>Reg. Date:</label>
                    <span><?php echo htmlspecialchars($record['registration_date']); ?></span>
                </div>
            </div>

            <div class="qr-section">
                <div id="qrcode"></div>
                <div class="validation-info">
                    <p><strong>Scan QR Code to Verify</strong></p>
                    <p>Registered by: <?php echo htmlspecialchars(strtoupper($record['registered_by'])); ?></p>
                </div>
            </div>

            <div class="card-footer">
                <p class="note">This identification card is only for use at La Huerta Health Center. Every time you visit the health center, you must present this card.</p>
            </div>
        </div>
    </div>

    <button onclick="window.print()" class="print-btn">
        <i class="fas fa-print"></i> Print ID Card
    </button>

    <script>
        // Timeout functionality
        let inactivityTimeout;
        
        function resetTimeout() {
            clearTimeout(inactivityTimeout);
            inactivityTimeout = setTimeout(function() {
                window.location.href = 'dashboard.php';
            }, 300000); // 5 minutes timeout
        }
        
        // Initialize timeout when page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetTimeout();
            
            // Reset timeout on user activity
            document.addEventListener('mousemove', resetTimeout);
            document.addEventListener('keypress', resetTimeout);
            document.addEventListener('click', resetTimeout);
            document.addEventListener('scroll', resetTimeout);
            
            // Add event listeners to form elements to reset timeout
            const formElements = document.querySelectorAll('input, select, textarea, button');
            formElements.forEach(element => {
                element.addEventListener('focus', resetTimeout);
                element.addEventListener('change', resetTimeout);
            });
        });

        // Generate QR Code
        var qrcode = new QRCode(document.getElementById("qrcode"), {
            text: <?php echo json_encode($validation_url); ?>,
            width: 120,  // Decreased size to 120
            height: 120, // Decreased size to 120
            colorDark : "#000000",
            colorLight : "#ffffff",
            correctLevel : QRCode.CorrectLevel.L  // Changed to L for better scanning
        });

        // Ensure QR code is properly sized
        window.onload = function() {
            var qrImage = document.querySelector("#qrcode img");
            if (qrImage) {
                qrImage.style.width = "120px";
                qrImage.style.height = "120px";
            }
            resetTimeout(); // Reset timeout after loading
        };
    </script>
</body>
</html>
<?php $conn->close(); ?> 