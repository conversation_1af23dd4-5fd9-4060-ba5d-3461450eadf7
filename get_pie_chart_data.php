<?php
session_start();
if (!isset($_SESSION['username'])) {
    http_response_code(403);
    exit('Unauthorized');
}

include "db.php";

// Initialize the response data
$data = [
    'labels' => [],
    'datasets' => [
        [
            'data' => [],
            'backgroundColor' => [
                '#4db3a8', '#3498db', '#e74c3c', '#f1c40f', '#9b59b6',
                '#1abc9c', '#e67e22', '#34495e', '#7f8c8d', '#16a085'
            ],
            'borderColor' => '#ffffff',
            'borderWidth' => 2
        ]
    ]
];

// Get period filter from request
$period = isset($_GET['period']) ? $_GET['period'] : 'all';

// Build date filter based on period
$dateFilter = '';
switch ($period) {
    case 'week':
        $dateFilter = "AND registration_date >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)";
        break;
    case 'month':
        $dateFilter = "AND registration_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
        break;
    case 'year':
        $dateFilter = "AND registration_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)";
        break;
    default:
        // All time - no filter
        $dateFilter = "";
}

// Get the count of registrations by each user
$sql = "SELECT registered_by, COUNT(*) as count
        FROM family_registration
        WHERE registered_by IS NOT NULL AND registered_by != ''
        $dateFilter
        GROUP BY registered_by
        ORDER BY count DESC";

$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // Format the user's name properly
        $username = ucwords(strtolower($row['registered_by']));
        $count = (int)$row['count'];

        // Add to the data arrays
        $data['labels'][] = $username;
        $data['datasets'][0]['data'][] = $count;
    }
}

// Add period info to the response
$data['period'] = $period;
$periodLabels = [
    'all' => 'All Time',
    'year' => 'This Year',
    'month' => 'This Month',
    'week' => 'This Week'
];
$data['periodLabel'] = $periodLabels[$period];

// Return JSON data
header('Content-Type: application/json');
echo json_encode($data);

$conn->close();
?>