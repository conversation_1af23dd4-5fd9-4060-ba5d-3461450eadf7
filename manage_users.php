<?php
session_start();
include "db.php";

// Check if user is logged in and is an admin
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'super admin')) {
    header("Location: dashboard.php");
    exit();
}

// Check for users inactive due to no login for 3 months
// First check if last_login column exists
$check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'last_login'");
if ($check_column->num_rows > 0) {
    $inactive_check_sql = "UPDATE users
                           SET status = 'inactive'
                           WHERE
                               role IN ('admin', 'staff')
                               AND status = 'active'
                               AND last_login IS NOT NULL
                               AND last_login < DATE_SUB(NOW(), INTERVAL 3 MONTH)";
    $conn->query($inactive_check_sql);

    // Find users who will be inactive soon
    $warning_sql = "SELECT id, username, fullname, role, last_login FROM users
                    WHERE
                        role IN ('admin', 'staff')
                        AND status = 'active'
                        AND last_login IS NOT NULL
                        AND last_login BETWEEN DATE_SUB(NOW(), INTERVAL 3 MONTH) AND DATE_SUB(NOW(), INTERVAL 2.5 MONTH)";
    $warning_users = $conn->query($warning_sql);
}

// Handle user actions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $username = trim($_POST['username']);
                $fullname = trim($_POST['fullname']);
                $designation = trim($_POST['designation']);
                $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                $plain_password = $_POST['password']; // Store the plain password temporarily
                $role = $_POST['role'];
                $status = 'active'; // Default status for new users

                // Prevent admin users from creating super admin accounts
                if ($_SESSION['role'] === 'admin' && $role === 'super admin') {
                    $error = "Regular admins cannot create super admin accounts.";
                    break;
                }

                // Check if username exists
                $check = $conn->prepare("SELECT id FROM users WHERE username = ?");
                $check->bind_param("s", $username);
                $check->execute();
                $result = $check->get_result();

                if ($result->num_rows > 0) {
                    $error = "Username already exists!";
                } else {
                    // Start transaction for creating user record
                    $conn->begin_transaction();

                    try {
                        // Insert user
                        $sql = "INSERT INTO users (username, fullname, designation, password, role, status) VALUES (?, ?, ?, ?, ?, ?)";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param("ssssss", $username, $fullname, $designation, $password, $role, $status);

                        if (!$stmt->execute()) {
                            throw new Exception("Error adding user: " . $stmt->error);
                        }

                        $user_id = $stmt->insert_id;
                        $stmt->close();

                        // Check if password_records table exists
                        $check_table = $conn->query("SHOW TABLES LIKE 'password_records'");
                        $has_password_records = $check_table->num_rows > 0;

                        // Store plain password if the table exists
                        if ($has_password_records) {
                            $password_sql = "INSERT INTO password_records (user_id, plain_password) VALUES (?, ?)";
                            $password_stmt = $conn->prepare($password_sql);
                            $password_stmt->bind_param("is", $user_id, $plain_password);

                            if (!$password_stmt->execute()) {
                                throw new Exception("Error storing password record: " . $password_stmt->error);
                            }

                            $password_stmt->close();
                        }

                        // Commit the transaction
                        $conn->commit();

                        $success = "User added successfully!";
                    } catch (Exception $e) {
                        // Rollback on error
                        $conn->rollback();
                        $error = $e->getMessage();
                    }
                }
                break;

            case 'edit':
                $id = $_POST['user_id'];

                // Check if admin is trying to edit a super admin
                if ($_SESSION['role'] === 'admin') {
                    $check_role = $conn->prepare("SELECT role FROM users WHERE id = ?");
                    $check_role->bind_param("i", $id);
                    $check_role->execute();
                    $role_result = $check_role->get_result();

                    if ($role_result->num_rows > 0 && $role_result->fetch_assoc()['role'] === 'super admin') {
                        $error = "You cannot edit a super admin account.";
                        break;
                    }
                }

                $username = trim($_POST['username']);
                $fullname = trim($_POST['fullname']);
                $designation = trim($_POST['designation']);
                $role = $_POST['role'];
                $password = $_POST['password'];

                // Check if admin is trying to change role to super admin
                if ($_SESSION['role'] === 'admin' && $role === 'super admin') {
                    $error = "Regular admins cannot upgrade users to super admin.";
                    break;
                }

                // Start transaction for updating user
                $conn->begin_transaction();

                try {
                    if (!empty($password)) {
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        $sql = "UPDATE users SET username = ?, fullname = ?, designation = ?, password = ?, role = ? WHERE id = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param("sssssi", $username, $fullname, $designation, $hashed_password, $role, $id);

                        // Check if password_records table exists
                        $check_table = $conn->query("SHOW TABLES LIKE 'password_records'");
                        $has_password_records = $check_table->num_rows > 0;

                        // Store plain password if the table exists
                        if ($has_password_records) {
                            $password_sql = "INSERT INTO password_records (user_id, plain_password) VALUES (?, ?)";
                            $password_stmt = $conn->prepare($password_sql);
                            $password_stmt->bind_param("is", $id, $password);

                            if (!$password_stmt->execute()) {
                                throw new Exception("Error storing password record: " . $password_stmt->error);
                            }

                            $password_stmt->close();
                        }
                    } else {
                        $sql = "UPDATE users SET username = ?, fullname = ?, designation = ?, role = ? WHERE id = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param("ssssi", $username, $fullname, $designation, $role, $id);
                    }

                    if (!$stmt->execute()) {
                        throw new Exception("Error updating user: " . $stmt->error);
                    }

                    $stmt->close();

                    // Commit the transaction
                    $conn->commit();

                    $success = "User updated successfully!";
                } catch (Exception $e) {
                    // Rollback on error
                    $conn->rollback();
                    $error = $e->getMessage();
                }
                break;

            case 'delete':
                // Only super admin can delete users
                if ($_SESSION['role'] !== 'super admin') {
                    $error = "Only super admin users can delete accounts.";
                    break;
                }

                $id = $_POST['user_id'];

                // Check if admin is trying to delete a super admin
                if ($_SESSION['role'] === 'admin') {
                    $check_role = $conn->prepare("SELECT role FROM users WHERE id = ?");
                    $check_role->bind_param("i", $id);
                    $check_role->execute();
                    $role_result = $check_role->get_result();

                    if ($role_result->num_rows > 0 && $role_result->fetch_assoc()['role'] === 'super admin') {
                        $error = "You cannot delete a super admin account.";
                        break;
                    }
                }

                $new_user = isset($_POST['new_user']) ? $_POST['new_user'] : null;

                // Start transaction
                $conn->begin_transaction();

                try {
                    // Get current user's username
                    $username_sql = "SELECT username FROM users WHERE id = ?";
                    $username_stmt = $conn->prepare($username_sql);
                    $username_stmt->bind_param("i", $id);
                    $username_stmt->execute();
                    $result = $username_stmt->get_result();

                    if ($result->num_rows === 0) {
                        throw new Exception("User not found.");
                    }

                    $current_user = $result->fetch_assoc()['username'];

                    // Check for family records
                    $records_sql = "SELECT COUNT(*) as count FROM family_registration WHERE registered_by = ?";
                    $records_stmt = $conn->prepare($records_sql);
                    $records_stmt->bind_param("s", $current_user);
                    $records_stmt->execute();
                    $has_records = $records_stmt->get_result()->fetch_assoc()['count'] > 0;

                    if ($has_records && !$new_user) {
                        throw new Exception("This user has registered family records. Please select a new user to reassign the records to.");
                    }

                    if ($has_records) {
                        // First verify the new user exists and is an admin or super admin
                        $verify_sql = "SELECT username FROM users WHERE username = ? AND (role = 'admin' OR role = 'super admin') AND id != ?";
                        $verify_stmt = $conn->prepare($verify_sql);
                        $verify_stmt->bind_param("si", $new_user, $id);
                        $verify_stmt->execute();

                        if ($verify_stmt->get_result()->num_rows === 0) {
                            throw new Exception("Selected user for reassignment is not valid.");
                        }

                        // Update family records to new user
                        $update_sql = "UPDATE family_registration SET registered_by = ? WHERE registered_by = ?";
                        $update_stmt = $conn->prepare($update_sql);
                        $update_stmt->bind_param("ss", $new_user, $current_user);
                        if (!$update_stmt->execute()) {
                            throw new Exception("Failed to reassign family records: " . $update_stmt->error);
                        }
                    }

                    // Delete the user
                    $delete_sql = "DELETE FROM users WHERE id = ?";
                    $delete_stmt = $conn->prepare($delete_sql);
                    $delete_stmt->bind_param("i", $id);
                    if (!$delete_stmt->execute()) {
                        throw new Exception("Failed to delete user: " . $delete_stmt->error);
                    }

                    // If we got here, commit the transaction
                    $conn->commit();
                    $success = $has_records ? "User deleted and records reassigned successfully!" : "User deleted successfully!";

                } catch (Exception $e) {
                    // Something went wrong, rollback changes
                    $conn->rollback();
                    $error = "Error: " . $e->getMessage();
                }
                break;

            case 'toggle_status':
                $id = $_POST['user_id'];

                // Check if admin is trying to change status of a super admin
                if ($_SESSION['role'] === 'admin') {
                    $check_role = $conn->prepare("SELECT role FROM users WHERE id = ?");
                    $check_role->bind_param("i", $id);
                    $check_role->execute();
                    $role_result = $check_role->get_result();

                    if ($role_result->num_rows > 0 && $role_result->fetch_assoc()['role'] === 'super admin') {
                        $error = "You cannot change the status of a super admin account.";
                        break;
                    }
                }

                $current_status = $_POST['current_status'];
                $new_status = $current_status === 'active' ? 'inactive' : 'active';

                $sql = "UPDATE users SET status = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("si", $new_status, $id);

                if ($stmt->execute()) {
                    $success = "User status updated successfully!";
                } else {
                    $error = "Error updating user status: " . $stmt->error;
                }
                $stmt->close();
                break;
        }
    }
}

// Fetch all users
$users = $conn->query("SELECT * FROM users ORDER BY
    CASE role
        WHEN 'super admin' THEN 1
        WHEN 'admin' THEN 2
        WHEN 'staff' THEN 3
    END, username");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - Family Registration System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            min-height: 100vh;
            background: #f0f7ff;
        }

        /* Ensure text is displayed as entered by user */
        .users-table td {
            text-transform: none; /* This ensures text is displayed exactly as entered, not capitalized */
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }

        .dropdown-container.show {
            display: block;
        }

        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #000;
            font-size: 2rem;
        }

        .add-user-btn {
            padding: 0.8rem 1.5rem;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .add-user-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .success {
            background: #f0fff4;
            color: #000;
            border: 1px solid #c8f7d4;
        }

        .error {
            background: #fff5f5;
            color: #000;
            border: 1px solid #fde8e8;
        }

        .users-table {
            width: 100%;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        th {
            background: #f8f9fa;
            color: #000;
            font-weight: 600;
        }

        td {
            color: #000;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 0 2px;
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn i {
            font-size: 1.1rem;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .edit-btn {
            background-color: #3498db;
            color: white;
        }

        .edit-btn:hover {
            background-color: #2980b9;
        }

        .status-btn {
            background-color: #2ecc71;
            color: white;
        }

        .status-btn.inactive {
            background-color: #e74c3c;
        }

        .status-btn:hover {
            opacity: 0.9;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        .delete-btn:hover {
            background-color: #c0392b;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
            z-index: 1000;
            overflow-y: auto;
            padding: 20px 0;
        }

        .modal-content {
            background: white;
            padding: 2.5rem;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            animation: modalFadeIn 0.3s ease;
            margin: 20px auto;
            transform: translateY(0);
            /* Hide scrollbar but keep functionality */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer and Edge */
        }

        /* Hide scrollbar for Chrome, Safari and Opera */
        .modal-content::-webkit-scrollbar {
            display: none;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-header h2 {
            color: #000;
            font-size: 1.75rem;
            font-weight: 600;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.75rem;
            cursor: pointer;
            color: #7f8c8d;
            transition: color 0.3s ease;
            padding: 0.5rem;
            margin: -0.5rem;
        }

        .close-modal:hover {
            color: #4db3a8;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: #000;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.875rem;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            color: #000;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4db3a8;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2.5rem;
            padding-top: 1.5rem;
            border-top: 2px solid #f0f0f0;
        }

        .cancel-btn,
        .submit-btn {
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background: #f0f0f0;
            color: #000;
        }

        .cancel-btn:hover {
            background: #e0e0e0;
        }

        .submit-btn {
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            font-weight: 600;
        }

        .submit-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }

            .main-content {
                padding: 1rem;
            }

            .users-table {
                overflow-x: auto;
            }
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-block;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
        }

        .status-badge::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .status-badge:hover::before {
            left: 100%;
        }

        .status-badge.active {
            background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
            color: #2e7d32;
            box-shadow: 0 2px 8px rgba(46, 125, 50, 0.2);
        }

        .status-badge.active:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
            animation: pulse 2s infinite;
        }

        .status-badge.inactive {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            box-shadow: 0 2px 8px rgba(198, 40, 40, 0.2);
        }

        .status-badge.inactive:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(198, 40, 40, 0.3);
            animation: pulse 2s infinite;
        }

        .status-btn {
            background: #f8f9fa;
            border: 1px solid #4db3a8;
            color: #4db3a8;
        }

        .status-btn.active {
            background: #e8f5e9;
            border-color: #2e7d32;
            color: #2e7d32;
        }

        .status-btn.inactive {
            background: #ffebee;
            border-color: #c62828;
            color: #c62828;
        }

        .status-btn:hover {
            transform: translateY(-2px);
        }

        .status-btn.active:hover {
            background: #2e7d32;
            color: white;
        }

        .status-btn.inactive:hover {
            background: #c62828;
            color: white;
        }

        .no-action {
            color: #999;
            font-style: italic;
            font-size: 0.9rem;
        }

        /* Enhanced Animation Styles */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.9);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(4);
                opacity: 0;
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 5px rgba(77, 179, 168, 0.3);
            }
            50% {
                box-shadow: 0 0 20px rgba(77, 179, 168, 0.6);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(100%);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Animation Applications */
        .header {
            animation: slideInDown 0.8s ease-out forwards;
        }

        .add-user-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .add-user-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 3s infinite;
        }

        .add-user-btn:hover {
            transform: translateY(-3px) scale(1.02);
            animation: glow 2s infinite;
        }

        .message {
            animation: bounceIn 0.6s ease-out forwards;
            transform-origin: center;
        }

        .users-table {
            animation: slideInUp 0.8s ease-out forwards;
            animation-delay: 0.2s;
            opacity: 0;
        }

        table thead {
            animation: fadeInLeft 0.6s ease-out forwards;
            animation-delay: 0.4s;
            opacity: 0;
        }

        table thead th {
            position: relative;
            overflow: hidden;
        }

        table thead th::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(77, 179, 168, 0.1), transparent);
            animation: shimmer 4s infinite;
            animation-delay: 1s;
        }

        tbody tr {
            opacity: 0;
            animation: fadeInRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            transition: all 0.3s ease;
        }

        tbody tr:hover {
            transform: translateX(5px);
            background: linear-gradient(90deg, rgba(77, 179, 168, 0.05), rgba(77, 179, 168, 0.02));
            box-shadow: 0 2px 8px rgba(77, 179, 168, 0.1);
        }

        /* Enhanced staggered animation for table rows */
        tbody tr:nth-child(1) { animation-delay: 0.5s; }
        tbody tr:nth-child(2) { animation-delay: 0.6s; }
        tbody tr:nth-child(3) { animation-delay: 0.7s; }
        tbody tr:nth-child(4) { animation-delay: 0.8s; }
        tbody tr:nth-child(5) { animation-delay: 0.9s; }
        tbody tr:nth-child(6) { animation-delay: 1.0s; }
        tbody tr:nth-child(7) { animation-delay: 1.1s; }
        tbody tr:nth-child(8) { animation-delay: 1.2s; }
        tbody tr:nth-child(9) { animation-delay: 1.3s; }
        tbody tr:nth-child(10) { animation-delay: 1.4s; }
        tbody tr:nth-child(n+11) { animation-delay: 1.5s; }

        .action-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .action-btn::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .action-btn:active::before {
            width: 300px;
            height: 300px;
        }

        .action-btn:hover {
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .modal {
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .modal-content {
            animation: slideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
            transform: translateY(100%);
        }

        .form-group {
            position: relative;
            overflow: hidden;
        }

        .form-group input,
        .form-group select {
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            z-index: 1;
        }

        .form-group input:focus,
        .form-group select:focus {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(77, 179, 168, 0.2);
        }

        .form-group::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            transition: width 0.4s ease;
        }

        .form-group:focus-within::after {
            width: 100%;
        }

        /* Password visibility styles */
        .password-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .password-container input {
            flex: 1;
            padding-right: 40px;
        }

        .toggle-password {
            position: absolute;
            right: 10px;
            background: none;
            border: none;
            color: #555;
            cursor: pointer;
            font-size: 16px;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .toggle-password:hover {
            color: #4db3a8;
        }

        .password-note {
            display: block;
            color: #777;
            font-size: 0.85rem;
            margin-top: 5px;
            font-style: italic;
        }

        .password-info {
            display: block;
            color: #4db3a8;
            font-size: 0.85rem;
            margin-top: 5px;
        }

        #current_password {
            background-color: #f9f9f9;
            color: #333;
            font-weight: 500;
            border: 1px solid #ddd;
        }

        #current_password.not-retrievable {
            color: #e74c3c;
            font-style: italic;
        }

        .submit-btn, .cancel-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .submit-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 3s infinite;
        }

        .submit-btn:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 6px 20px rgba(77, 179, 168, 0.4);
        }

        .submit-btn:active {
            transform: translateY(0) scale(0.98);
        }

        .cancel-btn:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #e0e0e0, #d0d0d0);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Loading state for buttons */
        .btn-loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-loading::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced table cell animations */
        tbody td {
            transition: all 0.3s ease;
            position: relative;
        }

        tbody td:hover {
            background: rgba(77, 179, 168, 0.05);
            transform: scale(1.02);
        }

        /* Floating effect for action buttons container */
        tbody tr td:last-child {
            transition: all 0.3s ease;
        }

        tbody tr:hover td:last-child {
            transform: translateX(10px);
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link dropdown-btn">
                        <i class="fas fa-folder"></i>
                        Records
                    </a>
                    <div class="dropdown-container">
                        <a href="view_family_records.php" class="dropdown-link">
                            <i class="fas fa-table"></i>
                            View Records
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                            <i class="fas fa-exclamation-triangle"></i>
                            Duplicate Records
                        </a>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <a href="missing_family_numbers.php" class="dropdown-link">
                            <i class="fas fa-list-ol"></i>
                            Missing Family Numbers
                        </a>
                        <a href="recently_deleted.php" class="dropdown-link">
                            <i class="fas fa-trash-alt"></i>
                            Recently Deleted
                        </a>
                        <?php endif; ?>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link active">
                        <i class="fas fa-users-cog"></i>
                        Manage Users
                    </a>
                </li>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </div>

        <div class="main-content">
            <div class="header">
                <h1>Manage Users</h1>
                <button class="add-user-btn" onclick="showModal('addUserModal')">
                    <i class="fas fa-user-plus"></i>
                    Add New User
                </button>
            </div>

            <?php if (isset($success)): ?>
                <div class="message success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="message error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($warning_users) && $warning_users->num_rows > 0): ?>
                <div class="message warning" style="background-color: #fff7e6; border-left-color: #f39c12;">
                    <i class="fas fa-clock" style="color: #f39c12;"></i>
                    <strong>Inactivity Warning:</strong> The following users will be automatically deactivated soon due to 3 months of inactivity:
                    <ul style="margin-top: 8px; margin-left: 25px; list-style-type: circle;">
                        <?php while ($user = $warning_users->fetch_assoc()): ?>
                            <li>
                                <?php
                                echo htmlspecialchars($user['username']) . " (" . htmlspecialchars($user['fullname']) . ") - ";
                                echo "Last login: " . ($user['last_login'] ? date('Y-m-d', strtotime($user['last_login'])) : "Never logged in");
                                ?>
                            </li>
                        <?php endwhile; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="users-table">
                <table>
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Designation</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($user = $users->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars(strtoupper($user['fullname'])); ?></td>
                                <td><?php echo htmlspecialchars(strtoupper($user['designation'])); ?></td>
                                <td><?php echo htmlspecialchars($user['role']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo isset($user['status']) ? $user['status'] : 'active'; ?>">
                                        <?php echo isset($user['status']) ? ucfirst($user['status']) : 'Active'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($_SESSION['role'] === 'super admin' || $user['role'] !== 'super admin'): ?>
                                    <button class="action-btn edit-btn"
                                            data-id="<?php echo $user['id']; ?>"
                                            data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                            data-fullname="<?php echo htmlspecialchars(strtoupper($user['fullname'])); ?>"
                                            data-designation="<?php echo htmlspecialchars(strtoupper($user['designation'])); ?>"
                                            data-role="<?php echo htmlspecialchars($user['role']); ?>"
                                            title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn status-btn <?php echo isset($user['status']) && $user['status'] === 'active' ? 'active' : 'inactive'; ?>"
                                            onclick="toggleStatus(<?php echo $user['id']; ?>, '<?php echo isset($user['status']) ? $user['status'] : 'active'; ?>')"
                                            title="<?php echo isset($user['status']) && $user['status'] === 'active' ? 'Deactivate User' : 'Activate User'; ?>">
                                        <i class="fas <?php echo isset($user['status']) && $user['status'] === 'active' ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                                    </button>
                                    <?php if ($_SESSION['role'] === 'super admin'): ?>
                                    <button class="action-btn delete-btn"
                                            onclick="confirmDelete(<?php echo $user['id']; ?>)"
                                            title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                    <?php else: ?>
                                    <span class="no-action">^_^</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New User</h2>
                <button class="close-modal" onclick="hideModal('addUserModal')">&times;</button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="fullname">Full Name</label>
                    <input type="text" id="fullname" name="fullname" required>
                </div>
                <div class="form-group">
                    <label for="designation">Designation</label>
                    <input type="text" id="designation" name="designation" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>
                <div class="form-group">
                    <label for="role">Role</label>
                    <select name="role" id="role" required>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <option value="super admin">Super Admin</option>
                        <?php endif; ?>
                        <option value="admin">Admin</option>
                        <option value="staff">Staff</option>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-btn" onclick="hideModal('addUserModal')">Cancel</button>
                    <button type="submit" class="submit-btn">Add User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Edit User</h2>
                <button class="close-modal" onclick="hideModal('editUserModal')">&times;</button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="user_id" id="edit_user_id">
                <div class="form-group">
                    <label for="edit_username">Username</label>
                    <input type="text" id="edit_username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="edit_fullname">Full Name</label>
                    <input type="text" id="edit_fullname" name="fullname" required>
                </div>
                <div class="form-group">
                    <label for="edit_designation">Designation</label>
                    <input type="text" id="edit_designation" name="designation" required>
                </div>
                <?php if ($_SESSION['role'] === 'super admin'): ?>
                <div class="form-group">
                    <label for="current_password">Current Password</label>
                    <div class="password-container">
                        <input type="password" id="current_password" readonly>
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility('current_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="password-note">* If showing "(password not retrievable)", it means this user's password is only stored in hashed form. Set a new password below to update it.</small>
                </div>
                <?php endif; ?>
                <div class="form-group">
                    <label for="edit_password">New Password <?php if ($_SESSION['role'] !== 'super admin'): ?>(leave blank to keep current)<?php endif; ?></label>
                    <div class="password-container">
                        <input type="password" id="edit_password" name="password" <?php if ($_SESSION['role'] === 'super admin'): ?>placeholder="Enter new password"<?php endif; ?>>
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility('edit_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <?php if ($_SESSION['role'] === 'super admin'): ?>
                    <small class="password-info">Setting a new password here will allow you to see it in the future when editing this user.</small>
                    <?php endif; ?>
                </div>
                <div class="form-group">
                    <label for="edit_confirm_password">Confirm New Password</label>
                    <div class="password-container">
                        <input type="password" id="edit_confirm_password" name="confirm_password">
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility('edit_confirm_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="edit_role">Role</label>
                    <select name="role" id="edit_role" required>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <option value="super admin">Super Admin</option>
                        <?php endif; ?>
                        <option value="admin">Admin</option>
                        <option value="staff">Staff</option>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-btn" onclick="hideModal('editUserModal')">Cancel</button>
                    <button type="submit" class="submit-btn">Update User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Confirm Delete</h2>
                <button class="close-modal" onclick="hideModal('deleteModal')">&times;</button>
            </div>
            <form method="POST" id="deleteForm">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="user_id" id="delete_user_id">
                <p>Are you sure you want to delete this user?</p>
                <div id="reassignSection" style="margin-top: 1rem; display: none;">
                    <p style="color: #e74c3c; margin-bottom: 1rem;">This user has registered family records. Please select a new user to reassign the records to:</p>
                    <div class="form-group">
                        <select name="new_user" id="new_user" required>
                            <?php
                            $users_sql = "SELECT username FROM users WHERE id != ? AND (role = 'admin' OR role = 'super admin')";
                            $users_stmt = $conn->prepare($users_sql);
                            $delete_id = isset($_POST['user_id']) ? $_POST['user_id'] : 0;
                            $users_stmt->bind_param("i", $delete_id);
                            $users_stmt->execute();
                            $users_result = $users_stmt->get_result();
                            while ($user = $users_result->fetch_assoc()) {
                                echo "<option value='" . htmlspecialchars($user['username']) . "'>" . htmlspecialchars($user['username']) . "</option>";
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-btn" onclick="hideModal('deleteModal')">Cancel</button>
                    <button type="submit" class="submit-btn" style="background: #e74c3c;">Delete User</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Timeout functionality
        let inactivityTimeout;

        function resetTimeout() {
            clearTimeout(inactivityTimeout);
            inactivityTimeout = setTimeout(function() {
                window.location.href = 'dashboard.php';
            }, 300000); // 5 minutes timeout
        }

        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            resetTimeout(); // Reset timeout when opening modal
        }

        function hideModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            resetTimeout(); // Reset timeout when closing modal
        }

        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const button = input.nextElementSibling;
            const iconElement = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                iconElement.classList.remove('fa-eye');
                iconElement.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                iconElement.classList.remove('fa-eye-slash');
                iconElement.classList.add('fa-eye');
            }
            resetTimeout(); // Reset timeout when toggling password
        }

        async function confirmDelete(userId) {
            resetTimeout(); // Reset timeout during delete operation
            try {
                // Check if user has records
                const response = await fetch(`check_user_records.php?user_id=${userId}`);
                const data = await response.json();

                if (data.error) {
                    alert(data.error);
                    return;
                }

                // Check if the user is a super admin and the current user is only an admin
                if (data.role === 'super admin' && '<?php echo $_SESSION['role']; ?>' === 'admin') {
                    alert('You cannot delete a super admin account.');
                    return;
                }

                document.getElementById('delete_user_id').value = userId;
                const reassignSection = document.getElementById('reassignSection');
                const deleteForm = document.getElementById('deleteForm');
                const newUserSelect = document.getElementById('new_user');

                if (data.has_records) {
                    reassignSection.style.display = 'block';
                    newUserSelect.required = true;
                    deleteForm.querySelector('p').textContent = `Are you sure you want to delete this user? They have ${data.record_count} family record(s) that need to be reassigned.`;
                } else {
                    reassignSection.style.display = 'none';
                    newUserSelect.required = false;
                    deleteForm.querySelector('p').textContent = 'Are you sure you want to delete this user?';
                }

                showModal('deleteModal');
            } catch (error) {
                alert('Error checking user records: ' + error.message);
            }
        }

        function toggleStatus(userId, currentStatus) {
            resetTimeout(); // Reset timeout when toggling status
            // Check if the user is trying to modify a super admin's status
            const targetRow = event.target.closest('tr');
            const userRole = targetRow.cells[3].innerText.trim();

            if (userRole === 'super admin' && '<?php echo $_SESSION['role']; ?>' === 'admin') {
                alert('You cannot change the status of a super admin account.');
                return;
            }

            if (confirm(`Are you sure you want to ${currentStatus === 'active' ? 'deactivate' : 'activate'} this user?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="user_id" value="${userId}">
                    <input type="hidden" name="current_status" value="${currentStatus}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        <?php if ($_SESSION['role'] === 'super admin'): ?>
        async function fetchUserPassword(userId) {
            resetTimeout(); // Reset timeout when fetching password
            try {
                const response = await fetch(`get_user_password.php?user_id=${userId}`);
                const data = await response.json();
                const passwordField = document.getElementById('current_password');

                if (data.success && data.password) {
                    // Show the retrieved password
                    passwordField.value = data.password;
                    passwordField.classList.remove('not-retrievable');
                } else {
                    // Show a message that the password cannot be retrieved
                    passwordField.value = '(password not retrievable)';
                    passwordField.classList.add('not-retrievable');
                }
            } catch (error) {
                console.error('Error fetching user password:', error);
                const passwordField = document.getElementById('current_password');
                passwordField.value = '(error retrieving password)';
                passwordField.classList.add('not-retrievable');
            }
        }
        <?php endif; ?>

        // Initialize timeout when page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetTimeout();

            // Reset timeout on user activity
            document.addEventListener('mousemove', resetTimeout);
            document.addEventListener('keypress', resetTimeout);
            document.addEventListener('click', resetTimeout);
            document.addEventListener('scroll', resetTimeout);
        });

        $(document).ready(function() {
            // Initialize dropdown functionality for sidebar
            $('.dropdown-btn').click(function(e) {
                e.preventDefault();
                $(this).toggleClass('active');
                $(this).next('.dropdown-container').toggleClass('show');
                resetTimeout(); // Reset timeout when interacting with dropdown
            });

            // Edit user
            $('.edit-btn').click(function() {
                resetTimeout(); // Reset timeout when editing user
                var userId = $(this).data('id');
                var username = $(this).data('username');
                var fullname = $(this).data('fullname');
                var designation = $(this).data('designation');
                var role = $(this).data('role');

                $('#edit_user_id').val(userId);
                $('#edit_username').val(username);
                $('#edit_fullname').val(fullname);
                $('#edit_designation').val(designation);

                <?php if ($_SESSION['role'] === 'super admin'): ?>
                // Fetch the user's password
                fetchUserPassword(userId);
                <?php endif; ?>

                // Check if the role option exists (for super admin option when viewed by admin users)
                if (role === 'super admin' && '<?php echo $_SESSION['role']; ?>' === 'admin') {
                    // Add a temporary option if needed
                    $('#edit_role').append($('<option>', {
                        value: 'super admin',
                        text: 'Super Admin'
                    }));
                }

                $('#edit_role').val(role);

                showModal('editUserModal');
            });

            // Close modals
            $('.close-modal, .cancel-btn').click(function() {
                $(this).closest('.modal').hide();
                resetTimeout(); // Reset timeout when closing modal
            });

            // Close modal when clicking outside
            $(window).click(function(e) {
                if ($(e.target).hasClass('modal')) {
                    $('.modal').hide();
                    resetTimeout(); // Reset timeout when closing modal
                }
            });

            // Form validation
            $('form').submit(function(e) {
                resetTimeout(); // Reset timeout when submitting form
                var password = $(this).find('input[name="password"]').val();
                var confirm_password = $(this).find('input[name="confirm_password"]').val();

                if (confirm_password && password !== confirm_password) {
                    e.preventDefault();
                    alert('Passwords do not match!');
                }

                // Additional validation for delete form
                if (this.id === 'deleteForm') {
                    const reassignSection = document.getElementById('reassignSection');
                    const newUserSelect = document.getElementById('new_user');

                    if (reassignSection.style.display === 'block' && !newUserSelect.value) {
                        e.preventDefault();
                        alert('Please select a user to reassign the family records to.');
                    }
                }
            });

            // Enhanced animation effects for better interactivity

            // Enhanced action button animations with ripple effect
            $('.action-btn').on('click', function(e) {
                const button = $(this);
                const ripple = $('<span class="ripple"></span>');

                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.css({
                    position: 'absolute',
                    width: size + 'px',
                    height: size + 'px',
                    left: x + 'px',
                    top: y + 'px',
                    background: 'rgba(255, 255, 255, 0.5)',
                    borderRadius: '50%',
                    transform: 'scale(0)',
                    animation: 'ripple 0.6s linear',
                    pointerEvents: 'none'
                });

                button.append(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);

                resetTimeout();
            });

            // Enhanced form field animations
            $('.form-group input, .form-group select').on('focus', function() {
                $(this).parent().addClass('focused');
                $(this).css({
                    'transform': 'translateY(-2px)',
                    'box-shadow': '0 4px 12px rgba(77, 179, 168, 0.2)'
                });
                resetTimeout();
            }).on('blur', function() {
                $(this).parent().removeClass('focused');
                $(this).css({
                    'transform': 'translateY(0)',
                    'box-shadow': 'none'
                });
            });

            // Enhanced table row animations
            $('tbody tr').each(function(index) {
                $(this).css('animation-delay', (0.5 + index * 0.1) + 's');
            });

            // Add staggered cell animations on row hover
            $('tbody tr').hover(
                function() {
                    const cells = $(this).find('td');
                    cells.each(function(index) {
                        setTimeout(() => {
                            $(this).css({
                                'transform': 'translateY(-2px)',
                                'box-shadow': '0 2px 8px rgba(77, 179, 168, 0.1)'
                            });
                        }, index * 50);
                    });
                    resetTimeout();
                },
                function() {
                    $(this).find('td').css({
                        'transform': 'translateY(0)',
                        'box-shadow': 'none'
                    });
                }
            );

            // Add loading states to forms
            $('form').on('submit', function() {
                const submitBtn = $(this).find('.submit-btn');
                const originalText = submitBtn.text();

                submitBtn.addClass('btn-loading');
                submitBtn.text('Processing...');

                // Simulate loading (remove this in production if not needed)
                setTimeout(() => {
                    submitBtn.removeClass('btn-loading');
                    submitBtn.text(originalText);
                }, 2000);

                resetTimeout();
            });

            // Enhanced modal animations
            $('.modal').on('show', function() {
                $(this).find('.modal-content').css({
                    'transform': 'translateY(100%)',
                    'opacity': '0'
                }).animate({
                    'transform': 'translateY(0)',
                    'opacity': '1'
                }, 500);
            });

            // Add smooth scroll to top when table loads
            if ($('tbody tr').length > 0) {
                setTimeout(() => {
                    $('html, body').animate({
                        scrollTop: $('.users-table').offset().top - 100
                    }, 800);
                }, 1000);
            }

            // Enhanced status badge interactions
            $('.status-badge').on('mouseenter', function() {
                $(this).css({
                    'transform': 'scale(1.05) rotate(2deg)',
                    'z-index': '10'
                });
            }).on('mouseleave', function() {
                $(this).css({
                    'transform': 'scale(1) rotate(0deg)',
                    'z-index': '1'
                });
            });
        });
    </script>
</body>
</html>
<?php $conn->close(); ?>